{"config": {"configFile": "/Users/<USER>/Desktop/boss/workspace-auto/webUIV1/playwright.config.js", "rootDir": "/Users/<USER>/Desktop/boss/workspace-auto/webUIV1/tests", "forbidOnly": false, "fullyParallel": false, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 1}, "preserveOutput": "always", "reporter": [["html", {"outputFolder": "./reports"}], ["json", {"outputFile": "./reports/test-results.json"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "/Users/<USER>/Desktop/boss/workspace-auto/webUIV1/videos", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "chromium", "name": "chromium", "testDir": "/Users/<USER>/Desktop/boss/workspace-auto/webUIV1/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 40000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.55.0", "workers": 1, "webServer": null}, "suites": [{"title": "直播详情-相关推荐_2025-09-06_02-18-15-770Z/C01_undefined_直播详情-相关推荐.spec.js", "file": "直播详情-相关推荐_2025-09-06_02-18-15-770Z/C01_undefined_直播详情-相关推荐.spec.js", "column": 0, "line": 0, "specs": [{"title": "直播详情-职位列表卡片点击", "ok": true, "tags": [], "tests": [{"timeout": 40000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 5080, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-09-06T02:18:18.641Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "bceb28e544719b310bf8-336fd6822402511745f2", "file": "直播详情-相关推荐_2025-09-06_02-18-15-770Z/C01_undefined_直播详情-相关推荐.spec.js", "line": 8, "column": 1}]}], "errors": [], "stats": {"startTime": "2025-09-06T02:18:18.110Z", "duration": 6361.647, "expected": 1, "skipped": 0, "unexpected": 0, "flaky": 0}}