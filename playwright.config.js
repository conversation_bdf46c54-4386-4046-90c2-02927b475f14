
// @ts-check
const { defineConfig, devices } = require('@playwright/test');

module.exports = defineConfig({
  testDir: './tests',
  timeout: 20000,
  fullyParallel: false, // 通过命令行参数控制并行性
  reporter: [
    ['html', { outputFolder: './reports' }],
    ['json', { outputFile: './reports/test-results.json' }]
  ],
  use: {
    baseURL: 'https://www.zhipin.com',
    trace: 'on-first-retry',
    video: 'on-first-retry',
    screenshot: 'only-on-failure',
    viewport: { width: 1440, height: 900 },
    headless: false,
    launchOptions: {
        slowMo: 100
      },
    storageState: 'tests/auth.json',
  },
  projects: [
    {
      name: 'chromium',
      use: {
        browserName: 'chromium',
      },
    },
  ],
  outputDir: './videos',
  retries: 0,
  // 默认worker数量，可通过命令行覆盖
  workers: 1,
});