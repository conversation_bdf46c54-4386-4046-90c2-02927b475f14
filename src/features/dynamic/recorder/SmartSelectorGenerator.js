const cheerio = require('cheerio');

/**
 * 智能选择器生成器
 * 专门生成适用于 Playwright 的元素定位代码
 */
class SmartSelectorGenerator {
  constructor() {
    // 选择器优先级配置（数值越小优先级越高）
    this.selectorPriority = {
      'locator-hierarchical': 1, // 多层组合选择器
      'locator-simplified': 1.5, // 简化选择器（新增）
    };
  }

  /**
   * 生成 Playwright 选择器
   * @param {string} html - HTML 内容
   * @param {Object} targetInfo - 目标元素信息
   * @returns {Object} 生成结果
   */
  generateSelectors(html, targetInfo) {
    try {
      const $ = cheerio.load(html);
      const selectors = [];

      // 2. 优化的多层组合选择器（智能跳级）
      const optimizedCombinedSelectors = this.generateOptimizedCombinedSelectors($, targetInfo);
      selectors.push(...optimizedCombinedSelectors);

      //3、新增简化选择器
      const simplifiedSelectors = this.generateSimplifiedSelectors($, targetInfo);
      selectors.push(...simplifiedSelectors);

      // 验证选择器
      const validatedSelectors = this.validateSelectors($, selectors, targetInfo);
      // 排序和推荐
      const sortedSelectors = this.sortAndRankSelectors(validatedSelectors);

      // 统计信息
      const uniqueSelectors = sortedSelectors.filter(s => s.isUnique);
      const multipleMatchSelectors = sortedSelectors.filter(s => !s.isUnique && s.matchCount > 1);

      return {
        success: true,
        data: {
          // === 推荐选择器 ===
          recommended: sortedSelectors[0] || null,              // 最优先推荐的选择器（排序后的第一个），如果没有则为null

          // === 备选选择器 ===
          alternatives: sortedSelectors.slice(1, 6),            // 备选选择器列表，最多返回5个（排除推荐的第一个）

          // === 完整选择器列表 ===
          all: sortedSelectors,                                 // 所有有效且已排序的选择器完整列表

          // === 分类选择器 ===
          uniqueSelectors: uniqueSelectors,                     // 所有唯一匹配的选择器（只匹配一个元素的选择器）
          multipleMatchSelectors: multipleMatchSelectors,       // 匹配多个元素的选择器（非唯一匹配的选择器）

          // === 统计信息 ===
          stats: {
            totalGenerated: selectors.length,                  // 总共生成的选择器数量（包括无效的）
            totalValid: sortedSelectors.length,                // 有效选择器的数量（通过验证的）
            uniqueCount: uniqueSelectors.length,               // 唯一匹配选择器的数量（最理想的选择器）
            multipleMatchCount: multipleMatchSelectors.length, // 多重匹配选择器的数量（需要进一步优化的）
            recommendedType: sortedSelectors[0]?.type || 'none', // 推荐选择器的类型（如：getByRole, getByTestId等）
            recommendedIsUnique: sortedSelectors[0]?.isUnique || false, // 推荐选择器是否为唯一匹配
            recommendedMatchCount: sortedSelectors[0]?.matchCount || 0  // 推荐选择器匹配到的元素数量
          }
        }
      };
      
    } catch (error) {
      console.error('❌ 生成 Playwright 选择器时出错:', error.message);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 生成优化的多层组合选择器（智能跳级版本）
   * 从当前元素向上查找唯一父元素，然后智能选择必要的层级构建选择器
   * @param {Object} $ - cheerio对象
   * @param {Object} targetInfo - 目标元素信息
   * @returns {Array} 优化的组合选择器数组
   */
  generateOptimizedCombinedSelectors($, targetInfo) {
    const selectors = [];

    try {
      // 首先找到目标元素
      const targetElement = this.findTargetElement($, targetInfo);
      if (!targetElement) {
        console.warn('未找到目标元素，无法生成优化组合选择器');
        return selectors;
      }

      // 向上查找唯一标识符，最多查找6层
      const maxLevels = 8;
      let currentElement = targetElement;
      let level = 0;

      while (currentElement && currentElement.length > 0 && level < maxLevels) {
        // 检查当前级别是否有唯一的标识符
        const uniqueIdentifier = this.findUniqueIdentifier($, currentElement);

        if (uniqueIdentifier) {

          // 找到唯一标识符，构建优化的层级选择器
          const optimizedSelector = this.buildOptimizedHierarchicalSelector($, currentElement, targetElement, uniqueIdentifier);
          if (optimizedSelector) {
            selectors.push({
              type: 'locator-hierarchical-optimized',
              selector: optimizedSelector.selector,
              priority: this.selectorPriority['locator-hierarchical'] - (0.1 * level), // 层级越少优先级越高
              stability: optimizedSelector.stability,
              description: `通过${level === 0 ? '当前元素' : `第${level}层父级`}的${uniqueIdentifier.type}优化定位${optimizedSelector.needsNth ? '（第' + (optimizedSelector.nthIndex + 1) + '个）' : ''}`,
              code: optimizedSelector.code,
              optimizedInfo: {
                startLevel: level,
                identifierType: uniqueIdentifier.type,
                identifierValue: uniqueIdentifier.value,
                needsNth: optimizedSelector.needsNth,
                nthIndex: optimizedSelector.nthIndex || 0,
                matchCount: optimizedSelector.matchCount,
                skippedLevels: optimizedSelector.skippedLevels || 0
              }
            });

            // 找到第一个有效的优化选择器就停止
            break;
          }
        }

        // 移动到父级
        currentElement = currentElement.parent();
        level++;
      }

    } catch (error) {
      console.warn('生成优化多层组合选择器时出错:', error);
    }

    return selectors;
  }

  /**
   * 生成多层组合选择器（保留原方法作为备用）
   * 从当前元素向上查找，如果有唯一的id或class，就从该级开始定位当前元素
   * 只返回层级数最少的那个选择器
   * @param {Object} $ - cheerio对象
   * @param {Object} targetInfo - 目标元素信息
   * @returns {Array} 组合选择器数组（最多包含一个最优选择器）
   */
  generateCombinedSelectors($, targetInfo) {
    const selectors = [];

    try {
      // 首先找到目标元素
      const targetElement = this.findTargetElement($, targetInfo);
      if (!targetElement) {
        console.warn('未找到目标元素，无法生成多层组合选择器');
        return selectors;
      }

      // 生成最优的层级选择器（层级数最少的）
      const bestSelector = this.generateBestHierarchicalSelector($, targetElement, targetInfo);
      if (bestSelector) {
        selectors.push(bestSelector);
      }

    } catch (error) {
      console.warn('生成多层组合选择器时出错:', error);
    }

    return selectors;
  }

  /**
   * 生成简化选择器
   * 向上查找最多5层，找到有唯一id或class的父级元素，然后直接定位到当前元素的标签+class，用nth()处理多个匹配
   * @param {Object} $ - cheerio对象
   * @param {Object} targetInfo - 目标元素信息
   * @returns {Array} 简化选择器数组
   */
  generateSimplifiedSelectors($, targetInfo) {
    const selectors = [];

    try {
      // 首先找到目标元素
      const targetElement = this.findTargetElement($, targetInfo);
      if (!targetElement) {
        console.warn('未找到目标元素，无法生成简化选择器');
        return selectors;
      }

      // 构建目标元素的基础选择器（标签+class）
      const targetSelector = this.buildTargetElementSelector($, targetElement);
      if (!targetSelector) {
        console.warn('无法构建目标元素选择器');
        return selectors;
      }

      // 向上查找最多5层，寻找有唯一标识符的父级
      let currentElement = targetElement;
      let level = 0;
      const maxLevels = 8;

      while (currentElement && currentElement.length > 0 && level < maxLevels) {
        // 检查当前级别是否有唯一的标识符
        const uniqueIdentifier = this.findUniqueIdentifier($, currentElement);

        if (uniqueIdentifier) {
          // 找到唯一标识符，构建简化选择器
          const simplifiedSelector = this.buildSimplifiedSelector($, uniqueIdentifier, targetSelector, targetElement);
          if (simplifiedSelector) {
            // 构建最终的选择器字符串和代码
            let finalSelector = `page.locator('${simplifiedSelector.selector}')`;
            let finalCode = `await page.locator('${simplifiedSelector.selector}')`;

            if (simplifiedSelector.needsNth) {
              finalSelector += `.nth(${simplifiedSelector.nthIndex})`;
              finalCode += `.nth(${simplifiedSelector.nthIndex})`;
            }
            finalCode += '.click();';

            selectors.push({
              type: 'locator-simplified',
              selector: finalSelector,
              priority: this.selectorPriority['locator-simplified'] - (0.1 * level), // 根据层级调整优先级
              stability: simplifiedSelector.stability,
              description: `通过${level === 0 ? '当前元素' : `第${level}层父级`}的${uniqueIdentifier.type}简化定位${simplifiedSelector.needsNth ? '（第' + (simplifiedSelector.nthIndex + 1) + '个）' : ''}`,
              code: finalCode,
              simplifiedInfo: {
                startLevel: level,
                identifierType: uniqueIdentifier.type,
                identifierValue: uniqueIdentifier.value,
                targetSelector: targetSelector,
                needsNth: simplifiedSelector.needsNth,
                nthIndex: simplifiedSelector.nthIndex || 0,
                matchCount: simplifiedSelector.matchCount
              }
            });

            // 找到第一个有效的简化选择器就停止，符合"简化"的理念
            break;
          }
        }

        // 移动到父级
        currentElement = currentElement.parent();
        level++;
      }

    } catch (error) {
      console.warn('生成简化选择器时出错:', error);
    }

    return selectors;
  }

  /**
   * 构建目标元素的基础选择器（标签+class）
   * @param {Object} $ - cheerio对象
   * @param {Object} targetElement - 目标元素
   * @returns {string|null} 目标元素选择器
   */
  buildTargetElementSelector($, targetElement) {
    try {
      const el = targetElement.get(0);
      if (!el) return null;

      let selector = el.tagName.toLowerCase();

      // 添加有意义的class
      const className = targetElement.attr('class');
      if (className && className.trim()) {
        const classes = className.trim().split(/\s+/).filter(c => c);
        const meaningfulClasses = this.filterMeaningfulClasses(classes);

        if (meaningfulClasses.length > 0) {
          // 只取前2个最有意义的class，避免选择器过于复杂
          const selectedClasses = meaningfulClasses.slice(0, 2);
          selector += '.' + selectedClasses.join('.');
        }
      }

      return selector;

    } catch (error) {
      console.warn('构建目标元素选择器时出错:', error);
      return null;
    }
  }

  /**
   * 构建简化选择器
   * @param {Object} $ - cheerio对象
   * @param {Object} uniqueIdentifier - 唯一标识符信息
   * @param {string} targetSelector - 目标元素选择器
   * @param {Object} targetElement - 目标元素对象（用于确定nth索引）
   * @returns {Object|null} 简化选择器信息
   */
  buildSimplifiedSelector($, uniqueIdentifier, targetSelector, targetElement) {
    try {
      // 构建完整的选择器：父级唯一标识符 + 空格 + 目标元素选择器
      const fullSelector = `${uniqueIdentifier.selector} ${targetSelector}`;

      // 验证选择器匹配的元素数量
      const matches = $(fullSelector);
      const matchCount = matches.length;

      if (matchCount === 0) {
        return null; // 没有匹配的元素
      }

      let needsNth = false;
      let nthIndex = 0;
      let stability = 'high';

      if (matchCount > 1) {
        // 有多个匹配，需要使用nth定位
        needsNth = true;

        // 找到目标元素在匹配列表中的实际位置
        nthIndex = this.findTargetElementIndex($, matches, targetElement);

        // 如果找不到目标元素，默认使用第一个
        if (nthIndex === -1) {
          nthIndex = 0;
        }

        stability = 'medium';
      }

      return {
        selector: fullSelector,
        needsNth: needsNth,
        nthIndex: nthIndex,
        matchCount: matchCount,
        stability: stability
      };

    } catch (error) {
      console.warn('构建简化选择器时出错:', error);
      return null;
    }
  }

  /**
   * 构建优化的层级选择器（智能跳级版本）
   * @param {Object} $ - cheerio对象
   * @param {Object} uniqueParentElement - 唯一父元素
   * @param {Object} targetElement - 目标元素
   * @param {Object} uniqueIdentifier - 唯一标识符信息
   * @returns {Object|null} 优化的选择器信息
   */
  buildOptimizedHierarchicalSelector($, uniqueParentElement, targetElement, uniqueIdentifier) {
    try {
      // 如果唯一父元素就是目标元素，直接返回
      if (uniqueParentElement.get(0) === targetElement.get(0)) {
        return {
          selector: `page.locator('${uniqueIdentifier.selector}')`,
          code: `await page.locator('${uniqueIdentifier.selector}').click();`,
          stability: 'high',
          needsNth: false,
          nthIndex: 0,
          matchCount: 1,
          skippedLevels: 0
        };
      }

      // 1. 收集从目标元素到唯一父元素的完整路径
      const path = this.collectElementPath($, targetElement, uniqueParentElement);
      // 2. 检测每一级在其父级上下文中的唯一性
      this.detectUniquenessInPath($, path);

      // 3. 构建优化选择器（只包含必要的级别）
      const optimizedResult = this.buildOptimizedSelectorFromPath($, path);

      if (optimizedResult) {
        // 构建完整的 Playwright 选择器和代码
        let finalSelector = `page.locator('${optimizedResult.selector}')`;
        let finalCode = `await page.locator('${optimizedResult.selector}')`;

        if (optimizedResult.needsNth) {
          finalSelector += `.nth(${optimizedResult.nthIndex})`;
          finalCode += `.nth(${optimizedResult.nthIndex})`;
        }
        finalCode += '.click();';

        return {
          selector: finalSelector,
          code: finalCode,
          stability: optimizedResult.stability,
          needsNth: optimizedResult.needsNth,
          nthIndex: optimizedResult.nthIndex,
          matchCount: optimizedResult.matchCount,
          skippedLevels: optimizedResult.skippedLevels
        };
      }

      return null;
    } catch (error) {
      console.warn('构建优化层级选择器时出错:', error);
      return null;
    }
  }

  /**
   * 查找目标元素
   * @param {Object} $ - cheerio对象
   * @param {Object} targetInfo - 目标元素信息
   * @returns {Object|null} 找到的目标元素
   */
  findTargetElement($, targetInfo) {
    try {
      // 如果有cssPath，直接使用
      if (targetInfo.cssPath) {
        const elements = $(targetInfo.cssPath);
        if (elements.length > 0) {
          return elements.first();
        }
      }

      // 根据属性查找元素
      let selector = targetInfo.tagName || '*';

      if (targetInfo.attributes) {
        if (targetInfo.attributes.id) {
          selector += `#${targetInfo.attributes.id}`;
        }
        if (targetInfo.attributes.class) {
          const classes = targetInfo.attributes.class.split(/\s+/).filter(c => c);
          if (classes.length > 0) {
            selector += '.' + classes.join('.');
          }
        }
      }

      const elements = $(selector);
      if (elements.length > 0) {
        // 如果有多个匹配，尝试通过文本内容进一步筛选
        if (elements.length > 1 && targetInfo.text) {
          const textMatches = elements.filter((_, el) => {
            return $(el).text().trim() === targetInfo.text.trim();
          });
          if (textMatches.length > 0) {
            return textMatches.first();
          }
        }
        return elements.first();
      }

      return null;
    } catch (error) {
      console.warn('查找目标元素时出错:', error);
      return null;
    }
  }

  /**
   * 生成最优的层级选择器（层级数最少的）
   * 从当前元素向上查找，找到第一个有唯一标识符的层级就停止
   * @param {Object} $ - cheerio对象
   * @param {Object} targetElement - 目标元素
   * @param {Object} targetInfo - 目标元素信息
   * @returns {Object|null} 最优的选择器对象
   */
  generateBestHierarchicalSelector($, targetElement, targetInfo) {
    const maxLevels = 8; // 最多向上查找6层

    let currentElement = targetElement;
    let level = 0;

    // 从当前元素开始向上遍历，找到第一个唯一标识符就停止
    while (currentElement && currentElement.length > 0 && level < maxLevels) {
      const element = currentElement.get(0);
      if (!element || !element.parent) break;

      // 检查当前级别是否有唯一的标识符
      const uniqueIdentifier = this.findUniqueIdentifier($, currentElement);

      if (uniqueIdentifier) {
        // 找到唯一标识符，构建选择器并立即返回
        const hierarchicalSelector = this.buildHierarchicalSelector($, currentElement, targetElement, uniqueIdentifier);
        if (hierarchicalSelector) {
          return {
            type: 'locator-hierarchical',
            selector: `page.locator('${hierarchicalSelector.selector}')`,
            priority: this.selectorPriority['locator-hierarchical'] - (0.1 * level), // 层级越少优先级越高
            stability: hierarchicalSelector.stability,
            description: `通过${level === 0 ? '当前元素' : `第${level}层父级`}的${uniqueIdentifier.type}定位`,
            code: `await page.locator('${hierarchicalSelector.selector}').click();`,
            hierarchyInfo: {
              startLevel: level,
              identifierType: uniqueIdentifier.type,
              identifierValue: uniqueIdentifier.value,
              needsNthChild: hierarchicalSelector.needsNthChild
            }
          };
        }
      }

      // 移动到父级
      currentElement = currentElement.parent();
      level++;
    }

    return null; // 没有找到合适的层级选择器
  }

  /**
   * 生成优化的层级选择器（保留原方法以备后用）
   * 从当前元素向上查找，如果有唯一的id或class，就从该级开始定位当前元素
   * @param {Object} $ - cheerio对象
   * @param {Object} targetElement - 目标元素
   * @param {Object} targetInfo - 目标元素信息
   * @returns {Array} 优化的选择器数组
   */
  generateOptimizedHierarchicalSelectors($, targetElement, targetInfo) {
    const selectors = [];
    const maxLevels = 8; // 最多向上查找6层

    let currentElement = targetElement;
    let level = 0;

    // 从当前元素开始向上遍历
    while (currentElement && currentElement.length > 0 && level < maxLevels) {
      const element = currentElement.get(0);
      if (!element || !element.parent) break;

      // 检查当前级别是否有唯一的标识符
      const uniqueIdentifier = this.findUniqueIdentifier($, currentElement);

      if (uniqueIdentifier) {
        // 找到唯一标识符，从这里开始构建选择器
        const hierarchicalSelector = this.buildHierarchicalSelector($, currentElement, targetElement, uniqueIdentifier);
        if (hierarchicalSelector) {
          selectors.push({
            type: 'locator-hierarchical',
            selector: `page.locator('${hierarchicalSelector.selector}')`,
            priority: this.selectorPriority['locator-hierarchical'] - (0.1 * level), // 层级越少优先级越高
            stability: hierarchicalSelector.stability,
            description: `通过${level === 0 ? '当前元素' : `第${level}层父级`}的${uniqueIdentifier.type}定位`,
            code: `await page.locator('${hierarchicalSelector.selector}').click();`,
            hierarchyInfo: {
              startLevel: level,
              identifierType: uniqueIdentifier.type,
              identifierValue: uniqueIdentifier.value,
              needsNthChild: hierarchicalSelector.needsNthChild
            }
          });
        }
      }

      // 移动到父级
      currentElement = currentElement.parent();
      level++;
    }

    return selectors;
  }

  /**
   * 查找元素的唯一标识符
   * @param {Object} $ - cheerio对象
   * @param {Object} element - 当前元素
   * @returns {Object|null} 唯一标识符信息
   */
  findUniqueIdentifier($, element) {
    try {
      const el = element.get(0);
      if (!el) {
        return null;
      }

      const tagName = el.tagName?.toLowerCase() || 'unknown';


      // 1. 检查ID是否唯一
      const id = element.attr('id');
      if (id && id.trim()) {
        const idElements = $(`#${id}`);
        if (idElements.length === 1) {
          return {
            type: 'ID',
            value: id,
            selector: `#${id}`,
            isUnique: true
          };
        } 
      } 

      // 2. 检查class组合是否唯一（先过滤动态class）
      const className = element.attr('class');
      if (className && className.trim()) {
        const classes = className.trim().split(/\s+/).filter(c => c);
        const meaningfulClasses = this.filterMeaningfulClasses(classes);
   
        if (meaningfulClasses.length > 0) {
          // 尝试不同的有意义class组合
          for (let i = 1; i <= meaningfulClasses.length; i++) {
            const selectedClasses = meaningfulClasses.slice(0, i);
            const classSelector = '.' + selectedClasses.join('.');

            // 使用过滤后的class进行唯一性检查
            const classElements = $(classSelector);
            if (classElements.length === 1) {
              return {
                type: 'Class',
                value: selectedClasses.join('.'),
                selector: classSelector,
                isUnique: true
              };
            }
          }

          // 如果单独的有意义class都不唯一，尝试完整的有意义class组合
          if (meaningfulClasses.length > 1) {
            const fullClassSelector = '.' + meaningfulClasses.join('.');
            const fullClassElements = $(fullClassSelector);
            if (fullClassElements.length === 1) {
              return {
                type: 'Class',
                value: meaningfulClasses.join('.'),
                selector: fullClassSelector,
                isUnique: true
              };
            }
          }
        }
      }

      // 3. 检查特殊属性是否唯一
      const specialAttrs = ['data-testid', 'data-test', 'name', 'role'];
      for (const attr of specialAttrs) {
        const attrValue = element.attr(attr);
        if (attrValue && attrValue.trim()) {
          const attrElements = $(`[${attr}="${attrValue}"]`);
          if (attrElements.length === 1) {
            return {
              type: 'Attribute',
              value: `${attr}="${attrValue}"`,
              selector: `[${attr}="${attrValue}"]`,
              isUnique: true
            };
          }
        }
      }
      return null;
    } catch (error) {
      console.warn('查找唯一标识符时出错:', error);
      return null;
    }
  }

  /**
   * 构建层级选择器
   * @param {Object} $ - cheerio对象
   * @param {Object} startElement - 起始元素（有唯一标识符的元素）
   * @param {Object} targetElement - 目标元素
   * @param {Object} uniqueIdentifier - 唯一标识符信息
   * @returns {Object|null} 层级选择器信息
   */
  buildHierarchicalSelector($, startElement, targetElement, uniqueIdentifier) {
    try {
      // 如果起始元素就是目标元素，直接返回
      if (startElement.get(0) === targetElement.get(0)) {
        return {
          selector: uniqueIdentifier.selector,
          stability: 'high',
          needsNthChild: false
        };
      }

      // 构建从起始元素到目标元素的路径
      const path = [];
      let current = targetElement;
      let needsNthChild = false;

      // 向上遍历直到找到起始元素
      while (current && current.length > 0) {
        const currentEl = current.get(0);
        const startEl = startElement.get(0);

        if (currentEl === startEl) {
          // 找到起始元素，停止遍历
          break;
        }

        // 构建当前元素的选择器部分
        const elementSelector = this.buildElementSelector($, current);
        if (elementSelector) {
          path.unshift(elementSelector.selector);
          if (elementSelector.needsNthChild) {
            needsNthChild = true;
          }
        }

        current = current.parent();
      }

      // 如果没有找到起始元素，返回null
      if (!current || current.length === 0) {
        return null;
      }

      // 组合完整的选择器 - 使用直接子元素选择器 > 确保精确匹配
      const fullSelector = uniqueIdentifier.selector + (path.length > 0 ? ' > ' + path.join(' > ') : '');

      // 验证选择器的唯一性
      const matches = $(fullSelector);
      let finalSelector = fullSelector;
      let finalNeedsNthChild = needsNthChild;

      // 如果匹配多个元素，尝试添加更精确的定位
      if (matches.length > 1) {
        const targetIndex = this.findElementIndex($, matches, targetElement);
        if (targetIndex >= 0) {
          // 可以考虑添加nth-child，但这里先保持原选择器
          finalNeedsNthChild = true;
        }
      }

      return {
        selector: finalSelector,
        stability: needsNthChild ? 'medium' : 'high',
        needsNthChild: finalNeedsNthChild,
        matchCount: matches.length
      };

    } catch (error) {
      console.warn('构建层级选择器时出错:', error);
      return null;
    }
  }

  /**
   * 构建单个元素的选择器部分
   * @param {Object} $ - cheerio对象
   * @param {Object} element - 元素
   * @returns {Object|null} 元素选择器信息
   */
  buildElementSelector($, element) {
    try {
      const el = element.get(0);
      if (!el) return null;

      let selector = el.tagName.toLowerCase();
      let needsNthChild = false;

      // 添加有意义的class
      const className = element.attr('class');
      if (className && className.trim()) {
        const classes = className.trim().split(/\s+/).filter(c => c);
        const meaningfulClasses = this.filterMeaningfulClasses(classes);

        if (meaningfulClasses.length > 0) {
          selector += '.' + meaningfulClasses.join('.');
        }
      }

      // 检查是否需要nth-child来确保唯一性
      const parent = element.parent();
      if (parent && parent.length > 0) {
        const siblings = parent.children(selector);
        if (siblings.length > 1) {
          const index = siblings.index(element) + 1;
          selector += `:nth-child(${index})`;
          needsNthChild = true;
        }
      }

      return {
        selector: selector,
        needsNthChild: needsNthChild
      };

    } catch (error) {
      console.warn('构建元素选择器时出错:', error);
      return null;
    }
  }

  /**
   * 在匹配的元素中找到目标元素的索引
   * @param {Object} $ - cheerio对象
   * @param {Object} matches - 匹配的元素集合
   * @param {Object} targetElement - 目标元素
   * @returns {number} 元素索引，-1表示未找到
   */
  findElementIndex($, matches, targetElement) {
    try {
      const targetEl = targetElement.get(0);
      let index = -1;

      matches.each((i, el) => {
        if (el === targetEl) {
          index = i;
          return false; // 停止遍历
        }
      });

      return index;
    } catch (error) {
      console.warn('查找元素索引时出错:', error);
      return -1;
    }
  }

  /**
   * 收集从目标元素到唯一父元素的完整路径
   * @param {Object} $ - cheerio对象
   * @param {Object} targetElement - 目标元素
   * @param {Object} uniqueParentElement - 唯一父元素
   * @returns {Array} 路径数组，每个元素包含级别信息
   */
  collectElementPath($, targetElement, uniqueParentElement) {
    const path = [];
    let current = targetElement;
    let level = 0;
    // 从目标元素向上收集路径
    while (current && current.length > 0) {
      const currentEl = current.get(0);
      const parentEl = uniqueParentElement.get(0);

      // 构建当前级别的选择器
      const elementSelector = this.buildElementSelectorForPath($, current);

      const pathNode = {
        element: current,
        selector: elementSelector,
        level: level,
        isTarget: currentEl === targetElement.get(0),
        isUniqueParent: currentEl === parentEl
      };

      path.unshift(pathNode); // 添加到数组开头，保持从父到子的顺序
      // 如果到达唯一父元素，停止收集
      if (currentEl === parentEl) {
        pathNode.isUnique = true; // 唯一父元素肯定是唯一的
        break;
      }

      current = current.parent();
      level++;
    }

    // 重新调整level，使其从0开始（父元素为0）
    path.forEach((node, index) => {
      node.level = index;
    });
    return path;
  }

  /**
   * 为路径构建专用的元素选择器
   * @param {Object} $ - cheerio对象
   * @param {Object} element - 元素
   * @returns {string} 元素选择器
   */
  buildElementSelectorForPath($, element) {
    try {
      const el = element.get(0);
      if (!el) return '';

      let selector = el.tagName.toLowerCase();

      // 添加有意义的class
      const className = element.attr('class');
      if (className && className.trim()) {
        const classes = className.trim().split(/\s+/).filter(c => c);
        const meaningfulClasses = this.filterMeaningfulClasses(classes);

        if (meaningfulClasses.length > 0) {
          // 只取前2个最有意义的class，避免选择器过于复杂
          const selectedClasses = meaningfulClasses.slice(0, 2);
          selector += '.' + selectedClasses.join('.');
        }
      }

      return selector;
    } catch (error) {
      console.warn('构建路径元素选择器时出错:', error);
      return '';
    }
  }

  /**
   * 检测路径中每一级在其父级上下文中的唯一性
   * @param {Object} $ - cheerio对象
   * @param {Array} path - 路径数组
   */
  detectUniquenessInPath($, path) {
    // 从第1级开始检测（跳过唯一父元素）
    for (let i = 1; i < path.length; i++) {
      const currentLevel = path[i];
      const parentContext = this.buildContextSelector(path, i - 1);

      // 在父级上下文中检测当前级别的唯一性
      const contextSelector = `${parentContext} > ${currentLevel.selector}`;
      const matches = $(contextSelector);

      currentLevel.isUnique = matches.length === 1;
      currentLevel.matchCount = matches.length;
    }
  }

  /**
   * 构建到指定级别的上下文选择器
   * @param {Array} path - 路径数组
   * @param {number} endIndex - 结束索引
   * @returns {string} 上下文选择器
   */
  buildContextSelector(path, endIndex) {
    const contextParts = [];
    for (let i = 0; i <= endIndex; i++) {
      if (path[i].isUnique || i === 0) {
        contextParts.push(path[i].selector);
      }
    }
    return contextParts.join(' ');
  }

  /**
   * 从路径构建优化的选择器（智能跳级）
   * @param {Object} $ - cheerio对象
   * @param {Array} path - 路径数组
   * @returns {Object|null} 优化选择器结果
   */
  buildOptimizedSelectorFromPath($, path) {
    try {
      const selectorParts = [path[0].selector]; // 从唯一父元素开始
      let hasNonUniqueTarget = false;
      let targetNthIndex = 0;
      let skippedLevels = 0;

      // 从第1级开始构建（跳过父元素）
      for (let i = 1; i < path.length; i++) {
        const currentLevel = path[i];

        if (currentLevel.isUnique) {
          // 唯一级别，直接添加
          selectorParts.push(currentLevel.selector);
        } else if (currentLevel.isTarget) {
          // 目标元素但不唯一，必须添加并准备nth处理
          selectorParts.push(currentLevel.selector);
          hasNonUniqueTarget = true;

          // 计算目标元素的nth索引
          const fullSelector = selectorParts.join(' ');
          const matches = $(fullSelector);
          targetNthIndex = this.findTargetElementIndex($, matches, currentLevel.element);

          if (targetNthIndex === -1) {
            console.warn('[选择器构建] 无法找到目标元素索引，使用默认值0');
            targetNthIndex = 0;
          }

        } else {
          // 非唯一的中间级别，跳过
          skippedLevels++;
        }
      }

      const finalSelector = selectorParts.join(' ');
      const finalMatchCount = hasNonUniqueTarget ? $(finalSelector).length : 1;

      return {
        selector: finalSelector,
        needsNth: hasNonUniqueTarget,
        nthIndex: targetNthIndex,
        matchCount: finalMatchCount,
        stability: hasNonUniqueTarget ? 'medium' : 'high',
        skippedLevels: skippedLevels
      };

    } catch (error) {
      console.warn('[选择器构建] 构建优化选择器时出错:', error);
      return null;
    }
  }

  /**
   * 在匹配的元素中找到目标元素的索引（专门用于简化选择器）
   * @param {Object} $ - cheerio对象
   * @param {Object} matches - 匹配的元素集合
   * @param {Object} targetElement - 目标元素
   * @returns {number} 元素索引，-1表示未找到
   */
  findTargetElementIndex($, matches, targetElement) {
    try {
      const targetEl = targetElement.get(0);
      if (!targetEl) {
        console.warn('目标元素为空');
        return -1;
      }

      let foundIndex = -1;

      // 遍历匹配的元素，找到与目标元素相同的那个
      matches.each((index, matchedEl) => {
        if (matchedEl === targetEl) {
          foundIndex = index;
          return false; // 停止遍历
        }
      });

      if (foundIndex === -1) {
        // 如果直接比较失败，尝试通过属性匹配
        const targetId = targetElement.attr('id');
        const targetClass = targetElement.attr('class');
        const targetText = targetElement.text().trim();

        matches.each((index, matchedEl) => {
          const $matched = $(matchedEl);
          let score = 0;

          // 比较ID
          if (targetId && $matched.attr('id') === targetId) {
            score += 3;
          }

          // 比较class
          if (targetClass && $matched.attr('class') === targetClass) {
            score += 2;
          }

          // 比较文本内容
          if (targetText && $matched.text().trim() === targetText) {
            score += 1;
          }

          // 如果匹配度足够高，认为是同一个元素
          if (score >= 2) {
            foundIndex = index;
            return false; // 停止遍历
          }
        });
      }

      return foundIndex;
    } catch (error) {
      console.warn('查找目标元素索引时出错:', error);
      return -1;
    }
  }

  /**
   * 验证选择器
   */
  validateSelectors($, selectors, targetInfo) {
    const validatedSelectors = selectors.map(selector => {
      try {
        // 检查选择器语法是否正确
        const isValid = this.isValidSelector(selector.selector);

        if (!isValid) {
          return {
            ...selector,
            isValid: false,
            matchCount: 0,
            elementIndex: -1,
            error: '无效的选择器语法'
          };
        }

        // 计算匹配的元素数量
        let matchCount;
        if ( selector.type === 'locator-hierarchical' || selector.type === 'locator-simplified') {
          // 这四种类型直接设置为 1（简化选择器已经处理了nth的情况）
          matchCount = 1;
        } else {
          // 其他类型使用原有逻辑
          matchCount = this.calculateMatchCount($, selector);
        }

        // 处理无法在当前环境验证的复杂 Playwright 选择器
        if (matchCount === -1) {
          return {
            ...selector,
            isValid: true,
            matchCount: '需要浏览器环境验证',
            isUnique: false,
            isSemanticallyUnique: false,
            elementIndex: -1,
            warning: '复杂的 Playwright 选择器无法在当前环境验证，建议使用 domAnalysisService.validatePlaywrightLocators() 方法在真实浏览器环境中验证'
          };
        }

        // 计算目标元素在匹配列表中的索引位置（新增功能）
        let elementIndex;
        if ( selector.type === 'locator-hierarchical' || selector.type === 'locator-simplified') {
          // 这四种类型都是唯一的，索引位置设置为 0（简化选择器已经处理了nth的情况）
          elementIndex = 0;
        } else {
          // 其他类型使用原有逻辑
          elementIndex = this.calculateElementIndex($, selector, targetInfo);
        }

        return {
          ...selector,
          isValid: true,
          matchCount: matchCount,
          isUnique: matchCount === 1,
          isSemanticallyUnique: matchCount === 1, // 🔧 新增：基于元素特征的唯一性
          elementIndex: elementIndex,
          warning: matchCount > 1 ? `匹配到 ${matchCount} 个元素，目标元素是第 ${elementIndex + 1} 个` : null
        };
      } catch (error) {
        return {
          ...selector,
          isValid: false,
          matchCount: 0,
          elementIndex: -1,
          error: error.message
        };
      }
    }).filter(s => s.isValid);

    // 🔧 修复bug：直接修改匹配多个元素的选择器，添加nth定位
    validatedSelectors.forEach(selector => {
      // 如果选择器匹配多个元素且能确定目标元素索引，直接修改选择器添加nth
      if (selector.matchCount > 1 && selector.elementIndex >= 0) {
        const nthSelector = this.generateNthSelector(selector, selector.elementIndex);
        if (nthSelector) {
          // 直接更新原选择器对象
          selector.selector = nthSelector.selector;
          selector.code = nthSelector.code;
          selector.description = nthSelector.description;
          selector.priority = nthSelector.priority;
          selector.matchCount = 1; // 更新匹配数量为1，因为添加了nth定位
        }
      }
    });

    return validatedSelectors;
  }

  /**
   * 为匹配多个元素的选择器生成nth版本
   * @param {Object} selector - 原选择器对象
   * @param {number} elementIndex - 目标元素索引
   * @returns {Object|null} nth选择器对象，如果无法生成则返回null
   */
  generateNthSelector(selector, elementIndex) {
    try {
      // 只为支持链式调用的选择器生成nth版本
      const supportedTypes = ['locator-css'];

      if (!supportedTypes.includes(selector.type)) {
        return null;
      }

      // 生成nth选择器
      const nthSelector = `${selector.selector}.nth(${elementIndex})`;

      // 生成对应的代码
      let nthCode = selector.code;
      if (nthCode) {
        // 在第一个方法调用后插入.nth()
        nthCode = nthCode.replace(/(\.(click|fill|check|uncheck|hover|focus)\([^)]*\))/, `.nth(${elementIndex})$1`);
      }

      return {
        ...selector,
        type: 'nth',
        selector: nthSelector,
        code: nthCode,
        priority: selector.priority + 5, // 🔧 修复：nth选择器优先级降低，作为备选方案
        stability: 'low', // 🔧 修复：nth选择器稳定性降为low，因为依赖位置
        description: `${selector.description}（第 ${elementIndex + 1} 个）`,
        isUnique: true, // 技术上唯一，但语义价值较低
        isSemanticallyUnique: false, // 🔧 新增：标记这不是语义上的唯一
        matchCount: 1, // nth选择器总是匹配1个元素
        elementIndex: 0, // nth选择器的目标元素索引总是0
        warning: `依赖元素位置，页面结构变化时可能失效` // 🔧 修复：添加警告信息
      };
    } catch (error) {
      console.warn('生成nth选择器失败:', error);
      return null;
    }
  }


  /**
   * 计算选择器匹配的元素数量
   */
  calculateMatchCount($, selector) {
    try {
      const selectorText = selector.selector;
      let matchCount = 0;
      // 解析 Playwright 选择器并转换为 CSS 选择器进行计算
      if (selectorText.includes('page.getByTestId(')) {
        const match = selectorText.match(/getByTestId\(['"]([^'"]+)['"]\)/);
        if (match) {
          matchCount = $(`[data-testid="${match[1]}"]`).length;
          return matchCount;
        }
      }
      if (selectorText.includes('page.getByRole(')) {
        // getByRole 比较复杂，这里简化处理
        const roleMatch = selectorText.match(/getByRole\(['"]([^'"]+)['"](?:,\s*\{\s*name:\s*['"]([^'"]+)['"]\s*\})?\)/);
        if (roleMatch) {
          const role = roleMatch[1];
          const name = roleMatch[2];

          // 根据角色查找元素
          let elements = this.findElementsByRole($, role);

          // 如果有名称过滤，进一步筛选
          if (name) {
            const nameFilteredElements = elements.filter((_, el) => {
              const $el = $(el);
              const text = $el.text().trim();
              const ariaLabel = $el.attr('aria-label');
              return text === name || ariaLabel === name;
            });
            // 🔧 修复bug：过滤掉父子重复的元素（基于文本匹配的情况）
            elements = $(this.filterParentChildDuplicates($, nameFilteredElements));
          }

          matchCount = elements.length;
          return matchCount;
        }
      }
      if (selectorText.includes('page.getByText(')) {
        const textMatch = selectorText.match(/getByText\(['"]([^'"]+)['"](?:,\s*\{\s*exact:\s*(true|false)\s*\})?\)/);
        const regexMatch = selectorText.match(/getByText\(\/([^\/]+)\/\)/);

        if (textMatch) {
          const text = textMatch[1];
          const exact = textMatch[2] !== 'false';

          let matchingElements;
          if (exact) {
            matchingElements = $('*').filter((_, el) => $(el).text().trim() === text);
          } else {
            matchingElements = $('*').filter((_, el) => $(el).text().includes(text));
          }

          // 🔧 修复bug：过滤掉父子重复的元素，只保留最具体的匹配
          const filteredElements = this.filterParentChildDuplicates($, matchingElements);
          return filteredElements.length;
        } else if (regexMatch) {
          const pattern = regexMatch[1];
          try {
            const regex = new RegExp(pattern);
            const matchingElements = $('*').filter((_, el) => regex.test($(el).text()));
            // 🔧 修复bug：同样需要过滤父子重复
            const filteredElements = this.filterParentChildDuplicates($, matchingElements);
            return filteredElements.length;
          } catch (error) {
            console.warn('无效的正则表达式模式:', pattern, error.message);
            return 0;
          }
        }
      }
      if (selectorText.includes('page.locator(')) {
        const match = selectorText.match(/locator\(['"]([^'"]+)['"]\)/);
        if (match) {
          let cssSelector = match[1];

          // 处理特殊的 Playwright 选择器语法

          // 处理 xpath
          if (cssSelector.startsWith('xpath=')) {
            // cheerio 不支持 xpath，返回估计值
            return 1;
          }

          // 处理过滤器
          if (selectorText.includes('.filter(')) {
            const filterMatch = selectorText.match(/locator\(['"]([^'"]+)['"]\)\.filter\(\{\s*hasText:\s*['"]([^'"]+)['"]\s*\}\)/);
            if (filterMatch) {
              const baseSelector = filterMatch[1];
              const filterText = filterMatch[2];
              const baseElements = $(baseSelector);
              const filteredElements = baseElements.filter((_, el) => $(el).text().includes(filterText));
              // 🔧 修复bug：过滤掉父子重复的元素
              const finalElements = this.filterParentChildDuplicates($, filteredElements);
              return finalElements.length;
            }
          }

          // 处理 nth
          if (selectorText.includes('.nth(')) {
            return 1; // nth 总是返回单个元素
          }

          // 普通 CSS 选择器
          try {
            matchCount = $(cssSelector).length;
            return matchCount;
          } catch (e) {
            return 0;
          }
        }
      }

      return 0;
    } catch (error) {
      console.warn('计算选择器匹配数量时出错:', error);
      return 0;
    }
  }

  /**
   * 使用 Playwright API 准确验证选择器匹配数量
   * 这是推荐的验证方法，特别是对于复杂的 Playwright 选择器
   */
  async validateSelectorWithPlaywright(selectorText, page) {
    try {
      // 将 page.xxx() 转换为实际的 locator 对象
      let locator;

      if (selectorText.startsWith('page.')) {
        // 移除可能的方法调用（如 .click(), .fill() 等）
        const cleanSelector = selectorText.replace(/\.(click|fill|check|uncheck|hover|focus|blur|clear|press|type|selectOption|setInputFiles)\([^)]*\)$/, '');

        // 将 page.xxx() 转换为 page.xxx()
        const methodCall = cleanSelector.replace('page.', 'page.');

        try {
          // 使用 eval 执行 Playwright 方法（在受控环境中是安全的）
          locator = eval(methodCall);
        } catch (evalError) {
          console.warn('选择器执行失败:', evalError.message);
          return { count: 0, error: evalError.message };
        }
      } else {
        // 普通的 CSS 选择器
        locator = page.locator(selectorText);
      }

      // 使用 Playwright 原生 API 获取匹配数量
      const count = await locator.count();

      return {
        count,
        valid: count > 0,
        selector: selectorText
      };

    } catch (error) {
      console.warn('Playwright 验证失败:', error.message);
      return { count: 0, error: error.message };
    }
  }

  /**
   * 过滤掉父子重复的元素，只保留最具体的匹配
   * @param {Object} $ - cheerio对象
   * @param {Object} matchingElements - 匹配的元素集合
   * @returns {Array} 过滤后的元素数组
   */
  filterParentChildDuplicates($, matchingElements) {
    const elements = matchingElements.toArray();
    const filteredElements = [];

    for (let i = 0; i < elements.length; i++) {
      const currentElement = elements[i];
      let isChildOfOther = false;

      // 检查当前元素是否是其他匹配元素的子元素
      for (let j = 0; j < elements.length; j++) {
        if (i !== j) {
          const otherElement = elements[j];
          // 如果当前元素是其他元素的子元素，则跳过
          if ($(otherElement).find(currentElement).length > 0) {
            isChildOfOther = true;
            break;
          }
        }
      }

      // 只保留不是其他元素子元素的元素
      if (!isChildOfOther) {
        filteredElements.push(currentElement);
      }
    }

    return filteredElements;
  }

  /**
   * 计算目标元素在匹配列表中的索引位置
   * @param {Object} $ - cheerio对象
   * @param {Object} selector - 选择器对象
   * @param {Object} targetInfo - 目标元素信息
   * @returns {number} 元素索引，-1表示未找到
   */
  calculateElementIndex($, selector, targetInfo) {
    try {
      const selectorText = selector.selector;
      let elements = null;

      // 根据不同的选择器类型获取匹配的元素集合
      if (selectorText.includes('page.locator(')) {
        const match = selectorText.match(/locator\(['"]([^'"]+)['"]\)/);
        if (match) {
          let cssSelector = match[1];
          if (!cssSelector.startsWith('xpath=')) {
            try {
              elements = $(cssSelector);
            } catch (e) {
              // CSS选择器无效
            }
          }
        }
      }

      // 如果没有找到匹配的元素，返回-1
      if (!elements || elements.length === 0) {
        return -1;
      }

      // 如果只有一个匹配元素，返回0
      if (elements.length === 1) {
        return 0;
      }

      // 在匹配的元素中找到目标元素的索引
      let bestMatchIndex = -1;
      let bestMatchScore = -1;
      let sameScoreElements = []; // 存储相同分数的元素

      elements.each((index, element) => {
        const $element = $(element);
        let matchScore = 0;

        // 1. 比较文本内容（权重：3）
        const elementText = $element.text().trim();
        if (targetInfo.text && elementText === targetInfo.text.trim()) {
          matchScore += 3;
        } else if (targetInfo.text && elementText.includes(targetInfo.text.trim())) {
          matchScore += 1;
        }

        // 2. 比较标签名（权重：2）
        if (targetInfo.tagName && element.tagName.toLowerCase() === targetInfo.tagName.toLowerCase()) {
          matchScore += 2;
        }

        // 3. 比较属性（权重：1-2）
        if (targetInfo.attributes) {
          // ID 属性匹配（权重：2）
          if (targetInfo.attributes.id && $element.attr('id') === targetInfo.attributes.id) {
            matchScore += 2;
          }

          // class 属性匹配（权重：1）
          if (targetInfo.attributes.class && $element.attr('class') === targetInfo.attributes.class) {
            matchScore += 1;
          }

          // 其他属性匹配（权重：1）
          Object.keys(targetInfo.attributes).forEach(attr => {
            if (attr !== 'id' && attr !== 'class') {
              if ($element.attr(attr) === targetInfo.attributes[attr]) {
                matchScore += 1;
              }
            }
          });
        }

        // 收集匹配分数和索引
        if (matchScore > bestMatchScore) {
          bestMatchScore = matchScore;
          bestMatchIndex = index;
          sameScoreElements = [index]; // 重置相同分数的元素列表
        } else if (matchScore === bestMatchScore && matchScore > 0) {
          sameScoreElements.push(index); // 添加到相同分数的元素列表
        }
      });

      // 如果有多个相同分数的元素（通常是完全相同的元素），使用targetInfo.index来区分
      if (sameScoreElements.length > 1 && targetInfo.index !== undefined) {
        // 在相同分数的元素中，选择与targetInfo.index最接近的那个
        const targetIndexInSameScore = Math.min(targetInfo.index, sameScoreElements.length - 1);
        bestMatchIndex = sameScoreElements[targetIndexInSameScore];
      }

      // 如果没有找到明确的匹配，使用目标元素的索引（如果提供）
      if (bestMatchIndex === -1 && targetInfo.index !== undefined) {
        bestMatchIndex = Math.min(targetInfo.index, elements.length - 1);
      }

      // 如果还是没有找到，默认返回第一个
      if (bestMatchIndex === -1) {
        bestMatchIndex = 0;
      }

      return bestMatchIndex;

    } catch (error) {
      console.warn('计算元素索引时出错:', error);
      return 0; // 出错时默认返回第一个
    }
  }



  /**
   * 检查选择器是否有效
   */
  isValidSelector(selector) {
    // 基本的选择器语法检查
    if (!selector || typeof selector !== 'string') return false;

    // 检查是否是有效的 Playwright locator 选择器
    return selector.startsWith('page.locator');
  }

  /**
   * 排序和推荐选择器
   * 简化排序策略：只考虑 selectorPriority 顺序 + isUnique 唯一性
   */
  sortAndRankSelectors(selectors) {
    return selectors
      .filter(s => s.isValid)
      .sort((a, b) => {
        // 第一优先级：唯一性（唯一匹配优于多重匹配）
        if (a.isUnique !== b.isUnique) {
          return b.isUnique ? 1 : -1;
        }

        // 第二优先级：选择器优先级（数值越小优先级越高）
        if (a.priority !== b.priority) {
          return a.priority - b.priority;
        }

        // 如果优先级相同，按匹配数量排序（匹配元素越少越好）
        return a.matchCount - b.matchCount;
      })
      .map((selector, index) => ({
        ...selector,
        rank: index + 1,
        recommended: index === 0
      }));
  }

  /**
   * 过滤掉无意义的 class（颜色、大小、状态等）
   * @param {Array} classes - class 数组
   * @returns {Array} 过滤后的有意义的 class 数组
   */
  filterMeaningfulClasses(classes) {
    // 定义无意义的 class 模式
    const meaninglessPatterns = [
      // 状态相关（只过滤明显的动态状态）
      /^(hover|focus|visited|blur)$/i,
      /^(loading|pending)$/i,
      /^(active|actived|selected|checked|disabled|hidden|visible|open|closed)$/i,

      // 动态状态后缀（重点过滤）
      /-hover$/i,
      /-focus$/i,
      /-active$/i,
      /-actived$/i,
      /-disabled$/i,
      /-selected$/i,
      /-checked$/i,
      /-loading$/i,
      /-error$/i,
      /-success$/i,
      /-pending$/i,
      /-visible$/i,
      /-hidden$/i,
      /-open$/i,
      /-closed$/i,

      // 布局相关（只过滤明显的CSS工具类）
      /^(d-|display-|float-|clear-)/i,
      /^position-(absolute|relative|fixed|static|sticky)$/i,

      // 动画和过渡
      /^(animate|transition|transform|opacity)/i,
      /-animate$/i,
      /-transition$/i,

      // 通用状态类
      /^(is-|has-|can-|should-|will-)/i,

      // 数字结尾的通用类
      /^[a-z]+-\d+$/i,

      // 单字母或很短的类
      /^[a-z]{1,2}$/i,

      // 临时状态类
      /^temp-/i,
      /^tmp-/i,
      /-temp$/i,
      /-tmp$/i,

      // 框架生成的动态类
      /^ng-/i,        // Angular
      /^v-/i,         // Vue
      /^css-/i,       // CSS-in-JS
      /^emotion-/i,   // Emotion
      /^styled-/i,    // Styled-components
      /-[a-z0-9]{8,}$/i,     // 随机生成的hash类名（8位以上且包含数字字母混合）
      /-[0-9a-f]{6,}$/i      // 十六进制hash类名
    ];

    return classes.filter(cls => {
      // 过滤掉匹配无意义模式的 class
      return !meaninglessPatterns.some(pattern => pattern.test(cls));
    });
  }

  /**
   * 向上查找父级 class 选择器
   * @param {Object} element - 当前元素
   * @param {Array} currentClasses - 当前元素的有意义 class
   * @returns {Array} 父级 class 选择器数组
   */
  generateParentClassSelectors(element, currentClasses) {
    const selectors = [];
    let currentElement = element.parent;
    let level = 1;
    const maxLevels =8;

    while (currentElement && level <= maxLevels) {
      const parentClasses = currentElement.attributes?.class;

      if (parentClasses) {
        const parentClassList = parentClasses.split(/\s+/).filter(c => c && !c.includes(' '));
        const meaningfulParentClasses = this.filterMeaningfulClasses(parentClassList);

        if (meaningfulParentClasses.length > 0) {
          // 为每个有意义的父级 class 生成选择器
          meaningfulParentClasses.forEach(parentClass => {
            if (currentClasses.length > 0) {
              // 使用父级 class 定位子元素的 class
              currentClasses.forEach(childClass => {
                const basicSelector = `.${parentClass} .${childClass}`;
                
                // 注意：这个方法生成的是后代选择器（空格），
                // 如果页面结构复杂，可能会匹配到多个元素，精确度不如子选择器（>）
                let finalSelector = basicSelector;
                
                const selector = `page.locator('${finalSelector}')`;
                selectors.push({
                  type: 'locator-class',
                  selector: selector,
                  priority: this.selectorPriority['locator-class'] - (0.1 * level), // 层级越少优先级越高
                  stability: matches?.length > 1 ? 'medium' : 'high',
                  description: `通过父级 ${parentClass} 定位子元素 ${childClass}（第${level}层）${matches?.length > 1 ? ' + 位置索引' : ''}`,
                  code: `await page.locator('${finalSelector}').click();`
                });
              });
            }

            // 如果当前元素没有有意义的 class，直接使用父级 class
            if (currentClasses.length === 0) {
              const selector = `page.locator('.${parentClass}')`;
              selectors.push({
                type: 'locator-class',
                selector: selector,
                priority: this.selectorPriority['locator-class'] - (0.1 * level),
                stability: 'medium',
                description: `通过父级 ${parentClass} 定位（第${level}层）`,
                code: `await page.locator('.${parentClass}').click();`
              });
            }
          });

          // 如果找到了有意义的父级 class，可以考虑停止向上查找
          // 但这里继续查找以提供更多选项
        }
      }

      currentElement = currentElement.parent;
      level++;
    }

    return selectors;
  }

  /**
   * 验证单个选择器
   */
  validateSelector(selector) {
    try {
      // 这里简化处理，实际应该解析 Playwright 选择器并转换为 CSS 选择器进行验证
      const isValid = this.isValidSelector(selector);

      return {
        success: true,
        data: {
          isValid: isValid,
          matchCount: isValid ? 1 : 0,
          selector: selector
        }
      };
    } catch (error) {
      return {
        success: false,
        data: {
          isValid: false,
          matchCount: 0,
          error: error.message
        }
      };
    }
  }

  /**
   * 验证选择器是否有效
   * @param {string} selector - 选择器字符串
   * @returns {boolean} 是否有效
   */
  isValidSelector(selector) {
    try {
      // 基本的选择器格式验证
      if (!selector || typeof selector !== 'string') {
        return false;
      }

      // 检查是否包含 locator 方法
      return selector.includes('locator');
    } catch (error) {
      return false;
    }
  }

  /**
   * 为HTML中的所有元素生成智能选择器
   * @param {string} html - HTML 内容
   * @returns {Array} 元素选择器列表，每个元素包含以下字段：
   * {
   *   index: number,           // 元素在页面中的索引位置
   *   tagName: string,         // HTML标签名（如：div, button, input等）
   *   text: string,            // 元素的文本内容（截取前100字符）
   *   attributes: object,      // 元素的所有HTML属性（如：id, class, name等）
   *   recommended: object,     // 推荐的最佳选择器
   *   alternatives: array,     // 备选选择器列表
   *   stats: object           // 选择器生成统计信息
   * }
   */
  generateSelectorsForAllElements(html) {
    try {
      const $ = cheerio.load(html);                    // 使用cheerio解析HTML
      const elementSelectors = [];                     // 存储所有元素的选择器信息
      let index = 0;                                   // 元素索引计数器

      const allElements = $('*');                      // 获取HTML中的所有元素

      // 遍历HTML中的所有元素
      allElements.each((_, element) => {
        try {
          const $element = $(element);                 // 包装为cheerio对象
          const tagName = element.tagName.toLowerCase(); // 获取标签名并转为小写

          // 收集元素的所有HTML属性
          const attributes = {};
          if (element.attribs) {
            Object.assign(attributes, element.attribs);
          }

          // 获取元素的文本内容
          const text = $element.text().trim();

          // 构建目标元素信息，用于传递给选择器生成器
          const targetInfo = {
            index: index++,                            // 元素索引（自增）
            tagName: tagName,                          // HTML标签名
            text: text.substring(0, 100),              // 文本内容（限制长度避免过长）
            attributes: attributes                     // 所有HTML属性
          };

          // 调用核心方法生成选择器
          const selectorResult = this.generateSelectors(html, targetInfo);
          // 如果成功生成选择器且有推荐选择器
          if (selectorResult.success && selectorResult.data.recommended) {
            const recommended = selectorResult.data.recommended;     // 推荐的最佳选择器
            const alternatives = selectorResult.data.alternatives || []; // 备选选择器列表

            // 构建完整的元素选择器信息对象
            const elementSelector = {
              // === 基本元素信息 ===
              index: targetInfo.index,                 // 元素在页面中的索引位置
              tagName: targetInfo.tagName,             // HTML标签名（如：div, button, input）
              text: targetInfo.text,                   // 元素文本内容（截取前100字符）
              attributes: targetInfo.attributes,       // 元素的所有HTML属性对象
              domInfo: this.generateDomInfo(targetInfo.tagName, targetInfo.attributes, targetInfo.text), // 完整的DOM信息展示

              // === 推荐的最佳选择器 ===
              recommended: {
                type: recommended.type,                // 选择器类型（如：getByRole, getByTestId, locator等）
                selector: recommended.selector,        // 完整的Playwright选择器字符串
                code: recommended.code,                // 可执行的Playwright代码
                priority: recommended.priority,        // 优先级数值（越小优先级越高）
                stability: recommended.stability,      // 稳定性等级（high/medium/low）
                description: recommended.description,  // 选择器的中文描述说明
                isUnique: recommended.isUnique,        // 是否唯一匹配（true表示只匹配一个元素）
                matchCount: recommended.matchCount,    // 匹配到的元素数量
                elementIndex: recommended.elementIndex // 目标元素在匹配列表中的索引位置（0表示第1个，1表示第2个，以此类推）
              },

              // === 备选选择器列表 ===
              alternatives: alternatives.map(alt => ({
                type: alt.type,                        // 备选选择器类型
                selector: alt.selector,                // 备选选择器字符串
                code: alt.code,                        // 备选选择器的可执行代码
                priority: alt.priority,                // 备选选择器优先级
                stability: alt.stability,              // 备选选择器稳定性
                description: alt.description,          // 备选选择器描述
                isUnique: alt.isUnique,                // 备选选择器是否唯一匹配
                matchCount: alt.matchCount             // 备选选择器匹配数量
              })),

              // === 统计信息 ===
              stats: selectorResult.data.stats        // 选择器生成的详细统计信息
              /*
               * stats 对象包含：
               * - totalGenerated: 总共生成的选择器数量
               * - totalValid: 有效选择器数量
               * - uniqueCount: 唯一匹配选择器数量
               * - multipleMatchCount: 多重匹配选择器数量
               * - recommendedType: 推荐选择器的类型
               * - recommendedIsUnique: 推荐选择器是否唯一
               * - recommendedMatchCount: 推荐选择器匹配数量
               */
            };

            elementSelectors.push(elementSelector);   // 添加到结果数组
          }
        } catch (error) {
          console.warn(`⚠️ 为元素 ${index} (${tagName}) 生成选择器失败:`, error.message);
        }
      });


      return elementSelectors;                         // 返回所有元素的选择器信息数组

    } catch (error) {
      console.error('❌ 批量生成智能选择器失败:', error.message);
      return [];                                       // 出错时返回空数组
    }
  }

  /**
   * 生成元素的DOM信息展示
   * @param {Object} $element - cheerio包装的元素对象
   * @param {string} tagName - 标签名
   * @param {Object} attributes - 属性对象
   * @param {string} text - 文本内容
   * @returns {string} DOM信息字符串
   */
  generateDomInfo(tagName, attributes, text) {
    try {
      // 构建开始标签
      let domInfo = `<${tagName}`;

      // 添加属性
      if (attributes && Object.keys(attributes).length > 0) {
        const attrStrings = [];
        Object.entries(attributes).forEach(([key, value]) => {
          if (value !== null && value !== undefined) {
            // 转义引号
            const escapedValue = String(value).replace(/"/g, '&quot;');
            attrStrings.push(`${key}="${escapedValue}"`);
          }
        });
        if (attrStrings.length > 0) {
          domInfo += ' ' + attrStrings.join(' ');
        }
      }

      // 判断是否为自闭合标签
      const selfClosingTags = ['img', 'input', 'br', 'hr', 'meta', 'link', 'area', 'base', 'col', 'embed', 'source', 'track', 'wbr'];
      if (selfClosingTags.includes(tagName.toLowerCase())) {
        domInfo += ' />';
        return domInfo;
      }

      domInfo += '>';

      // 添加文本内容（如果有且不太长）
      if (text && text.trim()) {
        const displayText = text.length > 50 ? text.substring(0, 50) + '...' : text;
        domInfo += displayText;
      }

      // 添加结束标签
      domInfo += `</${tagName}>`;

      return domInfo;

    } catch (error) {
      console.warn('生成DOM信息失败:', error);
      return `<${tagName}>...</${tagName}>`;
    }
  }
}

module.exports = SmartSelectorGenerator;
