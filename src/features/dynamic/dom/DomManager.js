/**
 * DOM 管理器
 * 处理页面DOM获取和分析的具体业务逻辑
 */
class DomManager {
  constructor(playwrightManager) {
    this.playwrightManager = playwrightManager;

    // 初始化HTML清洗器
    const HtmlCleaner = require('./htmlCleaner');
    this.htmlCleaner = new HtmlCleaner();
  }



  /**
   * 检查浏览器是否就绪
   * @returns {boolean} 浏览器是否就绪
   */
  isReady() {
    return this.playwrightManager && this.playwrightManager.isReady();
  }

  /**
   * 获取页面DOM结构
   * @param {Object} options - 选项参数
   * @param {boolean} options.filtered - 是否使用htmlCleaner过滤DOM数据
   * @param {string} options.formatType - 过滤时的格式类型：'minify', 'prettify', 'none'
   * @param {boolean} options.includeIframes - 是否包含iframe内容，默认为true
   * @returns {Promise<Object>} DOM数据对象
   */
  async getPageDOM(options = {}) {
    const { filtered = false, formatType = 'minify', includeIframes = true } = options;
    // 检查浏览器是否已初始化
    if (!this.isReady()) {
      throw new Error("浏览器未初始化或页面不可用，请先调用 /init 接口初始化浏览器");
    }

    // 确保有可用的页面
    await this.playwrightManager.ensureActivePage();

    // 获取页面基本信息
    const pageInfo = await this.playwrightManager.getBrowserInfo();

    // 获取当前活跃页面（与pageInfo保持一致）
    const currentActivePage = await this.playwrightManager.getCurrentActivePage();
    if (!currentActivePage) {
      throw new Error("没有可用的活跃页面");
    }

    // 执行JavaScript获取DOM结构（使用当前活跃页面）
    const domData = await currentActivePage.evaluate(() => {
      // 递归获取元素信息的函数
      function getElementInfo(element, depth = 0, maxDepth = 10) {
        if (depth > maxDepth) {
          return null;
        }

        const info = {
          tagName: element.tagName.toLowerCase(),
          id: element.id || null,
          className: element.className || null,
          textContent: element.textContent ? element.textContent.trim().substring(0, 100) : null,
          attributes: {},
          children: [],
          position: null,
          visible: false,
          depth: depth
        };

        // 获取所有属性
        for (let attr of element.attributes) {
          info.attributes[attr.name] = attr.value;
        }

        // 获取元素位置和可见性
        try {
          const rect = element.getBoundingClientRect();
          const style = window.getComputedStyle(element);

          info.position = {
            x: Math.round(rect.x),
            y: Math.round(rect.y),
            width: Math.round(rect.width),
            height: Math.round(rect.height)
          };

          info.visible = rect.width > 0 && rect.height > 0 &&
                        style.visibility !== 'hidden' &&
                        style.display !== 'none' &&
                        style.opacity !== '0';
        } catch (e) {
          // 忽略获取位置信息的错误
        }

        // 递归获取子元素
        for (let child of element.children) {
          const childInfo = getElementInfo(child, depth + 1, maxDepth);
          if (childInfo) {
            info.children.push(childInfo);
          }
        }

        return info;
      }

      // 获取页面统计信息
      function getPageStats() {
        const allElements = document.querySelectorAll('*');
        const visibleElements = Array.from(allElements).filter(el => {
          const rect = el.getBoundingClientRect();
          const style = window.getComputedStyle(el);
          return rect.width > 0 && rect.height > 0 &&
                 style.visibility !== 'hidden' &&
                 style.display !== 'none' &&
                 style.opacity !== '0';
        });

        const elementsByTag = {};
        allElements.forEach(el => {
          const tag = el.tagName.toLowerCase();
          elementsByTag[tag] = (elementsByTag[tag] || 0) + 1;
        });

        return {
          totalElements: allElements.length,
          visibleElements: visibleElements.length,
          elementsByTag: elementsByTag,
          hasImages: document.images.length,
          hasLinks: document.links.length,
          hasForms: document.forms.length,
          hasInputs: document.querySelectorAll('input, textarea, select').length
        };
      }

      // 获取页面元信息
      function getPageMeta() {
        const meta = {
          title: document.title,
          url: window.location.href,
          domain: window.location.hostname,
          protocol: window.location.protocol,
          viewport: {
            width: window.innerWidth,
            height: window.innerHeight
          },
          scrollPosition: {
            x: window.scrollX,
            y: window.scrollY
          },
          documentReady: document.readyState,
          charset: document.characterSet,
          lang: document.documentElement.lang || null
        };

        // 获取meta标签信息
        const metaTags = {};
        document.querySelectorAll('meta').forEach(meta => {
          const name = meta.getAttribute('name') || meta.getAttribute('property');
          const content = meta.getAttribute('content');
          if (name && content) {
            metaTags[name] = content;
          }
        });
        meta.metaTags = metaTags;

        return meta;
      }

      // 生成精简的DOM结构（用于大模型）
      function getSimplifiedDom(element, depth = 0, maxDepth = 10) {
        if (depth > maxDepth) {
          return null;
        }

        // 获取元素的文本内容（只取直接文本，不包括子元素文本）
        let directText = '';
        for (let node of element.childNodes) {
          if (node.nodeType === 3) { // TEXT_NODE
            directText += node.textContent.trim();
          }
        }

        // 检查是否包含中文
        const hasChinese = /[\u4e00-\u9fa5]/.test(directText);

        // 获取有用的属性（有值的属性）
        const usefulAttrs = {};
        for (let attr of element.attributes) {
          if (attr.value && attr.value.trim() !== '') {
            // 只保留常用的重要属性
            if (['id', 'class', 'name', 'type', 'placeholder', 'value', 'href', 'src', 'alt', 'title', 'role', 'aria-label', 'data-testid', 'data-test', 'data-cy'].includes(attr.name)) {
              usefulAttrs[attr.name] = attr.value;
            }
          }
        }

        // 判断是否应该包含此元素
        const shouldInclude =
          hasChinese || // 包含中文文本
          usefulAttrs.id || // 有ID
          usefulAttrs.class || // 有class
          Object.keys(usefulAttrs).length > 0; // 有其他有用属性

        if (!shouldInclude) {
          // 如果当前元素不符合条件，检查子元素
          const children = [];
          for (let child of element.children) {
            const childInfo = getSimplifiedDom(child, depth + 1, maxDepth);
            if (childInfo) {
              children.push(childInfo);
            }
          }

          // 如果有符合条件的子元素，返回一个简化的容器
          if (children.length > 0) {
            return {
              tag: element.tagName.toLowerCase(),
              children: children
            };
          }

          return null;
        }

        // 构建简化的元素信息
        const simplified = {
          tag: element.tagName.toLowerCase()
        };

        // 添加有用的属性
        if (Object.keys(usefulAttrs).length > 0) {
          simplified.attrs = usefulAttrs;
        }

        // 添加中文文本
        if (hasChinese) {
          simplified.text = directText.substring(0, 50); // 限制文本长度
        }

        // 递归处理子元素
        const children = [];
        for (let child of element.children) {
          const childInfo = getSimplifiedDom(child, depth + 1, maxDepth);
          if (childInfo) {
            children.push(childInfo);
          }
        }

        if (children.length > 0) {
          simplified.children = children;
        }

        return simplified;
      }

      // 返回完整的DOM数据
      return {
        meta: getPageMeta(),
        stats: getPageStats(),
        dom: getElementInfo(document.documentElement),
        simplifiedDom: getSimplifiedDom(document.documentElement),
        timestamp: new Date().toISOString()
      };
    });

    // 如果需要包含iframe，获取iframe内容
    if (includeIframes) {
      try {
        const iframesData = await this._processAllIframes(currentActivePage);
        domData.iframes = iframesData;
      } catch (error) {
        console.warn('⚠️ 获取iframe内容失败:', error.message);
        domData.iframes = [];
      }
    } else {
      domData.iframes = [];
    }

    // 如果需要过滤，使用htmlCleaner处理DOM数据
    let processedDomData = domData;
    let filteredHtml = null;
    let filterStats = null;
    let elementSelectors = null;

    if (filtered) {
      try {
        // 获取页面HTML（使用当前活跃页面）
        const originalHtml = await currentActivePage.content();
        const originalSize = Buffer.byteLength(originalHtml, 'utf8');

        // 使用 htmlCleaner 执行清理
        const cleanedHtml = this.htmlCleaner.performFourLevelClean(originalHtml, formatType);
        const cleanedSize = Buffer.byteLength(cleanedHtml, 'utf8');

        // 计算压缩率
        const reduction = this.htmlCleaner.calculateReduction(originalSize, cleanedSize);

        filteredHtml = cleanedHtml;
        filterStats = {
          originalSize: this.htmlCleaner.formatBytes(originalSize),
          cleanedSize: this.htmlCleaner.formatBytes(cleanedSize),
          originalCharCount: originalHtml.length,
          cleanedCharCount: cleanedHtml.length,
          reductionPercentage: reduction.percentage,
          reductionText: reduction.text
        };

        // 生成元素智能识别代码
        try {
          elementSelectors = await this.generateElementSelectors();
        } catch (selectorError) {
          console.warn('生成智能选择器失败:', selectorError.message);
          elementSelectors = []; // 设置为空数组而不是null，保持数据结构一致性
        }
      } catch (error) {
        console.warn('DOM过滤失败，返回原始数据:', error.message);
      }
    }

    // 构建完整的响应数据
    const result = {
      pageInfo: {
        url: pageInfo.url,
        title: pageInfo.title,
        viewport: pageInfo.viewport,
        isConnected: pageInfo.isConnected
      },
      domData: processedDomData,
      timestamp: new Date().toISOString()
    };

    // 如果进行了过滤，添加过滤相关信息
    if (filtered) {
      result.filtered = true;
      result.filteredHtml = filteredHtml;
      result.filterStats = filterStats;
      result.formatType = formatType;
      result.elementSelectors = elementSelectors;
      result.elementSelectorsCount = elementSelectors ? elementSelectors.length : 0;
    }

    return result;
  }

  /**
   * 获取页面的 Aria Snapshot（专为 AI 优化的页面结构）
   * @returns {Promise<string>} Aria Snapshot 字符串
   */
  async getAriaSnapshot() {
    // 检查浏览器是否已初始化
    if (!this.isReady()) {
      throw new Error("浏览器未初始化或页面不可用，请先调用 /init 接口初始化浏览器");
    }

    // 确保有可用的页面
    await this.playwrightManager.ensureActivePage();

    // 获取当前活跃页面
    const currentActivePage = await this.playwrightManager.getCurrentActivePage();
    if (!currentActivePage) {
      throw new Error("没有可用的活跃页面");
    }

    try {
      // 使用 Playwright 的 ariaSnapshot 功能（使用当前活跃页面）
      const ariaSnapshot = await currentActivePage.locator('body').ariaSnapshot();
      return ariaSnapshot;
    } catch (error) {
      console.warn('获取 ariaSnapshot 失败:', error.message);
      throw new Error('获取页面 Aria Snapshot 失败: ' + error.message);
    }
  }



  /**
   * 清洗页面HTML
   * @param {string} formatType - 输出格式：'minify', 'prettify', 'none'
   * @returns {Promise<Object>} 清洗结果
   */
  async cleanPageHTML(formatType = 'minify') {
    // 检查浏览器是否已初始化
    if (!this.isReady()) {
      throw new Error("浏览器未初始化或页面不可用，请先调用 /init 接口初始化浏览器");
    }

    // 确保有可用的页面
    await this.playwrightManager.ensureActivePage();

    // 获取当前活跃页面
    const currentActivePage = await this.playwrightManager.getCurrentActivePage();
    if (!currentActivePage) {
      throw new Error("没有可用的活跃页面");
    }

    // 获取页面HTML（使用当前活跃页面）
    const originalHtml = await currentActivePage.content();
    const originalSize = Buffer.byteLength(originalHtml, 'utf8');

    // 使用 htmlCleaner 执行清理
    const cleanedHtml = this.htmlCleaner.performFourLevelClean(originalHtml, formatType);
    const cleanedSize = Buffer.byteLength(cleanedHtml, 'utf8');

    // 计算压缩率
    const reduction = this.htmlCleaner.calculateReduction(originalSize, cleanedSize);

    return {
      success: true,
      originalHtml: originalHtml,
      cleanedHtml: cleanedHtml,
      stats: {
        originalSize: this.htmlCleaner.formatBytes(originalSize),
        cleanedSize: this.htmlCleaner.formatBytes(cleanedSize),
        originalCharCount: originalHtml.length,
        cleanedCharCount: cleanedHtml.length,
        reductionPercentage: reduction.percentage,
        reductionText: reduction.text
      },
      timestamp: new Date().toISOString()
    };
  }

  /**
   * 清洗DOM字符串并生成智能元素列表
   * @param {string} domString - DOM字符串
   * @param {string} formatType - 输出格式：'minify', 'prettify', 'none'
   * @returns {Promise<Object>} 清洗结果和智能元素列表
   */
  async cleanDOMString(domString, formatType = 'minify') {
    try {
      // 验证输入参数
      if (!domString || typeof domString !== 'string') {
        throw new Error('DOM字符串不能为空且必须是字符串类型');
      }

      // 计算原始大小
      const originalSize = Buffer.byteLength(domString, 'utf8');

      // 使用 htmlCleaner 执行清理
      const cleanedHtml = this.htmlCleaner.performFourLevelClean(domString, formatType);
      const cleanedSize = Buffer.byteLength(cleanedHtml, 'utf8');

      // 计算压缩率
      const reduction = this.htmlCleaner.calculateReduction(originalSize, cleanedSize);

      // 生成智能元素列表
      const SmartSelectorGenerator = require('../recorder/SmartSelectorGenerator');
      const selectorGenerator = new SmartSelectorGenerator();

      // 使用SmartSelectorGenerator分析清洗后的HTML并生成选择器
      const smartElements = selectorGenerator.generateSelectorsForAllElements(cleanedHtml);

      return {
        success: true,
        message: "DOM字符串清洗和智能分析完成",
        originalHtml: domString,
        cleanedHtml: cleanedHtml,
        stats: {
          originalSize: this.htmlCleaner.formatBytes(originalSize),
          cleanedSize: this.htmlCleaner.formatBytes(cleanedSize),
          originalCharCount: domString.length,
          cleanedCharCount: cleanedHtml.length,
          reductionPercentage: reduction.percentage,
          reductionText: reduction.text,
          compressionRatio: reduction.text
        },
        smartElements: smartElements,
        smartElementsCount: smartElements.length,
        formatType: formatType,
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      console.error('❌ DOM字符串清洗失败:', error);
      throw error;
    }
  }

  /**
   * 生成页面元素的智能选择器列表
   * @param {Object} options - 选项参数
   * @param {Object} options.domData - 已获取的DOM数据（可选，如果不提供会重新获取）
   * @param {string} options.cleanedHtml - 已清洗的HTML（可选，如果不提供会重新清洗）
   * @returns {Promise<Array>} 元素选择器列表
   */
  async generateElementSelectors(options = {}) {
    // 检查浏览器是否已初始化
    if (!this.isReady()) {
      throw new Error("浏览器未初始化或页面不可用，请先调用 /init 接口初始化浏览器");
    }

    // 确保有可用的页面
    await this.playwrightManager.ensureActivePage();

    try {
      // 步骤1: 获取DOM数据（如果没有提供）
      let domData = options.domData;
      if (!domData) {
        const domResult = await this.getPageDOM({ filtered: false });
        domData = domResult.domData;
      }

      // 步骤2: 获取并清洗HTML（如果没有提供）
      let cleanedHtml = options.cleanedHtml;
      if (!cleanedHtml) {
        // 获取当前活跃页面
        const currentActivePage = await this.playwrightManager.getCurrentActivePage();
        if (!currentActivePage) {
          throw new Error("没有可用的活跃页面");
        }
        const originalHtml = await currentActivePage.content();
        cleanedHtml = this.htmlCleaner.performFourLevelClean(originalHtml, 'minify');
      }

      // 步骤3: 初始化智能选择器生成器
      const SmartSelectorGenerator = require('../recorder/SmartSelectorGeneratorV1.0');
      const selectorGenerator = new SmartSelectorGenerator();

      // 步骤4: 使用SmartSelectorGenerator分析清洗后的HTML并生成选择器

      // 直接使用SmartSelectorGenerator的批量处理能力
      const elementSelectors = selectorGenerator.generateSelectorsForAllElements(cleanedHtml);

      return elementSelectors;
    } catch (error) {
      console.error('生成智能选择器失败:', error);
      return [];
    }
  }

  /**
   * 处理页面中的所有iframe
   * @param {Page} page - 要处理的页面对象，如果不提供则使用当前活跃页面
   * @returns {Promise<Array>} iframe数据数组
   */
  async _processAllIframes(page = null) {
    // 如果没有提供页面，获取当前活跃页面
    if (!page) {
      page = await this.playwrightManager.getCurrentActivePage();
      if (!page) {
        throw new Error("没有可用的活跃页面");
      }
    }

    // 获取页面中所有iframe的基本信息
    const iframesList = await this._getIframesList(page);

    if (iframesList.length === 0) {
      return [];
    }

    // 并行处理所有iframe
    const iframePromises = iframesList.map(async (iframeInfo, index) => {
      return await this._getIframeContent(page, iframeInfo, index);
    });

    const results = await Promise.allSettled(iframePromises);

    const iframesData = [];
    results.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        iframesData.push(result.value);
      } else {
        iframesData.push({
          index: index,
          id: iframesList[index]?.id || null,
          src: iframesList[index]?.src || null,
          error: `获取iframe失败: ${result.reason.message}`,
          dom: null,
          method: 'failed'
        });
      }
    });

    return iframesData;
  }

  /**
   * 获取页面中所有iframe的基本信息
   * @param {Page} page - Playwright页面对象
   * @returns {Promise<Array>} iframe信息数组
   */
  async _getIframesList(page) {
    // 使用 Playwright 的 locator API 获取所有 iframe
    const iframeLocators = await page.locator('iframe').all();

    const iframesList = [];

    for (let i = 0; i < iframeLocators.length; i++) {
      const iframe = iframeLocators[i];

      try {
        // 获取 iframe 的属性信息
        const iframeInfo = await iframe.evaluate((element, index) => ({
          index: index,
          id: element.id || null,
          name: element.name || null,
          src: element.src || null,
          srcdoc: element.srcdoc || null,
          width: element.width || null,
          height: element.height || null,
          className: element.className || null,
          title: element.title || null
        }), i);

        iframesList.push(iframeInfo);

      } catch (error) {
        console.warn(`获取第 ${i + 1} 个iframe信息失败:`, error.message);
        iframesList.push({
          index: i,
          id: null,
          name: null,
          src: null,
          srcdoc: null,
          width: null,
          height: null,
          className: null,
          title: null,
          error: error.message
        });
      }
    }

    return iframesList;
  }

  /**
   * 获取单个iframe的内容
   * @param {Page} page - Playwright页面对象
   * @param {Object} iframeInfo - iframe基本信息
   * @param {number} index - iframe索引
   * @returns {Promise<Object>} iframe内容数据
   */
  async _getIframeContent(page, iframeInfo, index) {
    // 只用最靠谱的策略：直接获取iframe内容
    const result = await this._getIframeDomDirect(page, index);

    return {
      ...iframeInfo,
      dom: result.dom,
      error: result.error,
      method: result.method,
      stats: result.stats
    };
  }

  /**
   * 直接获取iframe内容（使用正确的Playwright API）
   * @param {Page} page - Playwright页面对象
   * @param {number} index - iframe索引
   * @returns {Promise<Object>} 获取结果
   */
  async _getIframeDomDirect(page, index) {
    try {
      // 等待页面加载完成
      await page.waitForLoadState('domcontentloaded');

      // 获取所有iframe元素
      const iframeElements = await page.$$('iframe');
      if (index >= iframeElements.length) {
        return {
          success: false,
          error: 'iframe索引超出范围',
          method: 'direct'
        };
      }

      const iframeElement = iframeElements[index];

      // 获取iframe的frame对象
      const contentFrame = await iframeElement.contentFrame();

      if (!contentFrame) {
        return {
          success: false,
          error: '无法访问iframe内容（可能是跨域限制）',
          method: 'direct'
        };
      }

      // 等待iframe内容加载
      await page.waitForTimeout(1000);

      // 获取iframe的HTML内容
      const iframeHTML = await contentFrame.content();

      // 新增：调用 cleanPageHTML 方法清洗iframe的HTML
      let cleanedIframeHTML = null;
      let cleanStats = null;
      try {
        const cleanedHtml = this.htmlCleaner.performFourLevelClean(iframeHTML, 'minify');
        const originalSize = Buffer.byteLength(iframeHTML, 'utf8');
        const cleanedSize = Buffer.byteLength(cleanedHtml, 'utf8');
        const reduction = this.htmlCleaner.calculateReduction(originalSize, cleanedSize);

        cleanedIframeHTML = cleanedHtml;
        cleanStats = {
          originalSize: this.htmlCleaner.formatBytes(originalSize),
          cleanedSize: this.htmlCleaner.formatBytes(cleanedSize),
          originalCharCount: iframeHTML.length,
          cleanedCharCount: cleanedHtml.length,
          reductionPercentage: reduction.percentage,
          reductionText: reduction.text
        };
      } catch (cleanError) {
        console.warn(`⚠️ 第${index + 1}个iframe HTML清洗失败:`, cleanError.message);
        cleanedIframeHTML = iframeHTML; // 如果清洗失败，使用原始HTML
      }

      // 新增：生成iframe页面的选择器列表
      let iframeSelectors = [];
      try {
        const SmartSelectorGenerator = require('../recorder/SmartSelectorGenerator');
        const selectorGenerator = new SmartSelectorGenerator();
        iframeSelectors = selectorGenerator.generateSelectorsForAllElements(cleanedIframeHTML);
      } catch (selectorError) {
        console.warn(`⚠️ 第${index + 1}个iframe生成选择器失败:`, selectorError.message);
        iframeSelectors = [];
      }

      return {
        success: true,
        dom: {
          html: iframeHTML,
          cleanedHtml: cleanedIframeHTML,
          elementSelectors: iframeSelectors,
          timestamp: new Date().toISOString()
        },
        method: 'direct',
        stats: {
          htmlSize: iframeHTML.length,
          cleanedHtmlSize: cleanedIframeHTML ? cleanedIframeHTML.length : 0,
          elementSelectorsCount: iframeSelectors.length,
          cleanStats: cleanStats
        }
      };

    } catch (error) {
      return {
        success: false,
        error: error.message,
        method: 'direct'
      };
    }
  }




}

module.exports = DomManager;
