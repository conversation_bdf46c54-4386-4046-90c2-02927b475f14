const { test, expect } = require('@playwright/test');

// 添加store
test.use({
  storageState: './tests/auth_14000001101.json'
});

test('直播详情-职位列表', async ({ page }) => {
  // 跳转
  await page.goto("https://live.zhipin.com/web/boss/webcast/liveContainer/mypreach/?back=header")
  // 等待网络
  await page.waitForLoadState("networkidle")
  //点击平台代码
  await page.locator('div.create-btn-card-wrap').nth(2).click();

  await page.locator('button.check-btn').click();
  // 等待元素出现

  await page.locator(".live-list").waitFor({ state: "visible" })

  //获取所有代播场次，断言大于1，且核心字段不为空

  // 获取所有代播场次列表项
  const liveItems = page.locator('.live-list > div');
  // 断言代播场次数量大于1
  const liveCount = await liveItems.count();
  expect(liveCount).toBeGreaterThan(1);
  liveItems.nth(0).click();
  //点击进入直播间按钮，跳转新页面
  // 点击进入直播间按钮，跳转新页面
  const pageNewPromise = page.waitForEvent('popup');
  await page.locator('button.btn-primary').click();
  const pageNew = await pageNewPromise;
  await pageNew.waitForLoadState('networkidle');
  // 重点：清除登录cookie
  await pageNew.context().clearCookies();
  //刷新
  await pageNew.reload()
  //等待元素出现
  await pageNew.locator("#tab-2").waitFor({ state: "visible" }) 
  // 点击职位tab
  await pageNew.locator('#tab-2').click();
  // 等待职位列表加载完成
  await pageNew.locator('.job-list').first().waitFor({ state: 'visible' });
  //获取职位列表长度，断言大于1，且核心字段不为空

  // 获取职位列表项
  const jobItems = pageNew.locator('.job-list');
  // 断言职位数量大于1
  const jobCount = await jobItems.count();
  expect(jobCount).toBeGreaterThan(1);
  //获取所有列表下job-item子列表，断言大于1，循环点击子列表

  // 获取所有职位组子列表项
  const jobSubItems = pageNew.locator('li.job-item');
  // 断言职位组数量大于1
  const subItemCount = await jobSubItems.count();
  expect(subItemCount).toBeGreaterThan(1);

  // 循环点击每个职位组（跳过第一个"全部职位"项）
  for (let i = 1; i < subItemCount; i++) {
    await jobSubItems.nth(i).click();
    //获取下边所有职位卡片，断言长度大于0，且核心字段不为空
    //等待
    await page.waitForTimeout(300)
    // 获取当前职位组下的所有职位卡片
    const jobCards = pageNew.locator('.job-list.b-scrollbar .job-item');
      // 断言职位卡片数量大于0
      const jobCardCount = await jobCards.count();
      expect(jobCardCount).toBeGreaterThan(0);
      await page.waitForTimeout(300)
      // 验证每个职位卡片的核心字段不为空
      for (let i = 0; i < jobCardCount; i++) {
        const currentCard = jobCards.nth(i);
        // 验证职位名称
        await expect(currentCard.locator('p')).not.toBeEmpty();
        // 验证薪资范围
        await expect(currentCard.locator('div').nth(1)).not.toBeEmpty();
        // 验证公司名称
        await expect(currentCard.locator('a.line-2')).not.toBeEmpty();
        // 验证工作地点
        await expect(currentCard.locator('.tag-item').first()).not.toBeEmpty();
      }

  }
  

});
