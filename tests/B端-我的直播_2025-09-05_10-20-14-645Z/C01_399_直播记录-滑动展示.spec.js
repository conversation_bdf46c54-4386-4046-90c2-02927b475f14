const { test, expect } = require('@playwright/test');

// 添加store
test.use({
  storageState: './tests/auth_14000001101.json'
});

test('直播详情-职位列表卡片点击', async ({ page }) => {
  // 跳转
  await page.goto("https://live.zhipin.com/web/boss/webcast/liveContainer/mypreach/?back=header")
  // 等待网络
  await page.waitForLoadState("networkidle")
  //查看全部
  await page.locator('.live-list-title-btn').click();
  await page.waitForLoadState("networkidle")
  //等待
  await page.waitForTimeout(1000)
  //模拟滑动，向下滑动多个页面

  // 模拟滑动操作 - 向下滑动多个页面
  for (let i = 0; i < 10; i++) {
    await page.mouse.wheel(0, 1000); // 每次向下滚动1000像素
    await page.waitForTimeout(500); // 等待滚动完成
  }

  // 等待页面稳定
  await page.waitForLoadState('networkidle');

  

});
