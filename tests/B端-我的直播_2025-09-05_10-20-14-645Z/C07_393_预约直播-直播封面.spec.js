const { test, expect } = require('@playwright/test');

// 添加store
test.use({
  storageState: './tests/auth_14000001101.json'
});

test('预约直播-直播封面', async ({ page }) => {
  // 跳转
  await page.goto("https://live.zhipin.com/web/boss/webcast/liveContainer/mypreach/?back=header")
  // 等待网络
  await page.waitForLoadState("networkidle")
  //点击平台代码
  await page.locator('div.create-btn-card-wrap').nth(3).click();
  //点击品牌，随机点击一个品牌，点击保存

  // 点击品牌输入框
  await page.locator('.brand-content input').click();
  //等待

  await page.waitForTimeout(200)
  // 随机选择一个品牌（选择第一个可见的品牌）
  const firstBrand = page.locator('.brand-item').first();
  await firstBrand.click();
  // 点击保存
  await page.locator('.save-btn').click();


  // 点击设置默认封面
  await page.locator('.extra:has-text("设置默认封面")').click();
  // 点击取消按钮
  await page.locator('.cancel').click();
  // 再次点击设置默认封面
  await page.locator('.extra:has-text("设置默认封面")').click();
  // 点击换一张按钮
  await page.locator('button:has-text("换一张")').click();
  // 点击保存按钮
  await page.locator('.sure').click();

 


});
