import { elementUtils } from '@/tools/ElementUtils';
import { test, expect } from '@playwright/test';

test.describe('BOSS直聘登录测试', () => {

  test('添加登录', async ({ page }) => {
    // 打开 Boss 直聘首页
    await page.goto('https://www.zhipin.com');

    // 删除指定域名下的 Cookie
    await page.context().clearCookies({ domain: '.zhipin.com' });
    // 刷新页面
    await page.reload();

    // 手动添加 cookie
    await page.context().addCookies([
      {
        name: 'wt2',
        value: 'DJMfLfz4LAcuO8zkOysANLh7xXPpb_s9dfsLJ_AHVfptq_xTT-auE6UkjGz5cev7_1nXWctWM8Zc9NLNvJ_0dqg~~',
        domain: '.zhipin.com',
        path: '/',
      },
    ]);

    // 等待页面加载完成
    await page.waitForSelector('body');
    // 刷新页面
    await page.reload();
    
    //如果有同意按钮点击同意
    const agreeButton = page.getByRole('button', { name: '同意', exact: true });
    if (await agreeButton.isVisible()) {
      await agreeButton.click();
    } 
    //存在就点击
    await elementUtils(page.getByRole('link', { name: '登录/注册' })).clickIfExists({ timeout: 3000 });

    // 验证“我要招聘”按钮不存在
    await expect(page.getByRole('link', { name: '我要招聘', exact: true })).not.toBeVisible();
    
    // 保存浏览器状态
    console.log('保存浏览器状态到:', './tests/auth_14000001101.json');
    await page.context().storageState({ path: './tests/auth_14000001101.json' });
    await page.context().storageState({ path: './tests/auth.json' });
    await page.waitForTimeout(5000);
  });
});






