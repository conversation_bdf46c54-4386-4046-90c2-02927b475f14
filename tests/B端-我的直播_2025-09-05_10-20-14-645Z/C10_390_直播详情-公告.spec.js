const { test, expect } = require('@playwright/test');

// 添加store
test.use({
  storageState: './tests/auth_14000001101.json'
});

test('直播详情-职位列表卡片点击', async ({ page }) => {
  // 跳转
  await page.goto("https://live.zhipin.com/web/boss/webcast/liveContainer/mypreach/?back=header")
  // 等待网络
  await page.waitForLoadState("networkidle")
  //点击平台代码
  await page.locator('div.create-btn-card-wrap').nth(2).click();

  await page.locator('button.check-btn').click();
  // 等待元素出现

  await page.locator(".live-list").waitFor({ state: "visible" })

  //获取所有代播场次，断言大于1，且核心字段不为空

  // 获取所有代播场次列表项
  const liveItems = page.locator('.live-list > div');
  // 断言代播场次数量大于1
  const liveCount = await liveItems.count();
  expect(liveCount).toBeGreaterThan(1);
  liveItems.nth(0).click();

  // 点击进入直播间按钮，跳转新页面
  const pageNewPromise = page.waitForEvent('popup');
  await page.locator('button.btn-primary').click();
  const pageNew = await pageNewPromise;
  await pageNew.waitForLoadState('networkidle');

  await pageNew.context().clearCookies();
  await pageNew.reload()
  await pageNew.locator("#tab-2").waitFor({ state: "visible" })

  //点击城市tab，获取下边所有子城市tab，循环点击

  // 点击公告
  await pageNew.locator('#tab-4').click();




});
