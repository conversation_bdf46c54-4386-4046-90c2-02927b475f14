const { test, expect } = require('@playwright/test');

// 添加store
test.use({
  storageState: './tests/auth_14000001101.json'
});

test('预约直播-设置-选择直播助理', async ({ page }) => {
  // 跳转
  await page.goto("https://live.zhipin.com/web/boss/webcast/liveContainer/mypreach/?back=header")
  // 等待网络
  await page.waitForLoadState("networkidle")
  //点击平台代码
  await page.locator('div.create-btn-card-wrap').nth(3).click();
  //点击高级直播
  // 点击高级直播选项
  await page.locator('label.radio-item:has-text("高级直播")').click();
  //选择直播时间

  // 点击选择直播时间输入框
  await page.locator('.selectTime input.input').click();
  // 等待日期选择器加载
  await page.waitForSelector('.datepicker-day');
  //选择直播时间，下午、晚上
  await page.locator('.cell.day.today').dblclick()
  //下午时间随机选一个，晚上时间随机选一个，通过class定位
  //等待

  await page.waitForTimeout(300)
  // 随机选择下午时间段
  const afternoonTimes = await page.locator('.time-select-box div:nth-child(1) div div span').all();
  const randomAfternoonIndex = Math.floor(Math.random() * afternoonTimes.length);
  await afternoonTimes[randomAfternoonIndex].click();

  // 随机选择晚上时间段
  const eveningTimes = await page.locator('.time-select-box div:nth-child(2) div div span').all();
  const randomEveningIndex = Math.floor(Math.random() * eveningTimes.length);
  await eveningTimes[randomEveningIndex].click();

  // 点击保存按钮
  await page.locator('button.sure:not(.disabled)').click();
  //直播间名称输入直播

  // 输入直播间名称
  await page.locator('input[placeholder="请输入内容"]').fill('直播测试');

  // 点击品牌输入框
  await page.locator('.brand-content input').click();
  //等待
  await page.waitForTimeout(200)
  // 随机选择一个品牌（选择第一个可见的品牌）
  const firstBrand = page.locator('.brand-item').first();
  await firstBrand.click();
  // 点击保存
  await page.locator('.save-btn').click();

  // 点击设置默认封面
  await page.locator('.extra:has-text("设置默认封面")').click();
  // 点击取消按钮
  await page.locator('.cancel').click();
  // 再次点击设置默认封面
  await page.locator('.extra:has-text("设置默认封面")').click();
  // 点击换一张按钮
  await page.locator('button:has-text("换一张")').click();
  // 点击保存按钮
  await page.locator('.sure').click();
  //点击下一步
  await page.locator('.btn.btn-primary.nextStep').click();
  //点击选择同事直播，默认断言同事列表长度大于0，且核心字段不为空

  // 点击直播助理
  await page.locator('.live-item.select-item').nth(1).click();
  // 等待同事列表加载
  await page.waitForSelector('.user-name');
  //等待

  await page.waitForTimeout(500)
  // 断言同事列表长度大于0
  const colleagueCount = await page.locator('.user-name').count();
  expect(colleagueCount).toBeGreaterThan(0);
  // 断言核心字段不为空
  const firstColleagueName = await page.locator('.user-name').first().textContent();
  expect(firstColleagueName).not.toBe('');

  //搜索框中输入涨，获取搜索结果列表断言长度大于0，然后点击保存

  // 在搜索框中输入"涨"
  await page.locator('.input-suggest input').fill('闫');
  //回车
  await page.press("body", "Enter")
  // 等待搜索结果加载
  await page.waitForTimeout(1000); // 等待搜索请求完成
  // 获取搜索结果列表并断言长度大于0
  const searchResults = await page.locator('.user-name').count();
  expect(searchResults).toBeGreaterThan(0);
  await page.locator('.user-name').nth(0).click();
  // 点击保存按钮
  await page.locator('.choose-footer .btn-primary').click();


});
