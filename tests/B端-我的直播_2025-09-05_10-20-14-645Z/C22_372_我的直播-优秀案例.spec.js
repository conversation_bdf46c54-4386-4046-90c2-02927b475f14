//新建
const { test, expect } = require('@playwright/test');
//添加store
test.use({
  storageState: './tests/auth_14000001101.json'
});

test('我的直播-优秀案例', async ({ page }) => {
  //跳转
  await page.goto("https://live.zhipin.com/web/boss/webcast/liveContainer/mypreach/?back=header")
  //等待网络
  await page.waitForLoadState("networkidle")
  //获取优秀案例列表，断言大于1，核心字段不为空，然后循环点击跳转新页面，加载完毕后关闭新页面
  // 获取优秀案例列表
  const exampleItems = page.locator('.live-example-item');
  const count = await exampleItems.count();
  // 断言列表数量大于1
  expect(count).toBeGreaterThan(1);

  // 循环处理每个案例
  for (let i = 0; i < count; i++) {
    const currentItem = exampleItems.nth(i);
    //断言观看人数和title不为空
    // 获取当前案例的观看人数和标题元素
    const viewerCount = await currentItem.locator('.live-example-desc').textContent();
    const title = await currentItem.locator('.live-example-title').textContent();
    
    // 断言观看人数和标题不为空
    expect(viewerCount).not.toBe('');
    expect(title).not.toBe('');
      // 点击案例跳转新页面
      const pageNewPromise = page.waitForEvent('popup');
      await currentItem.click();
      const pageNew = await pageNewPromise;
    
      // 等待新页面加载完成
      await pageNew.waitForLoadState('networkidle');
    
      // 关闭新页面
      await pageNew.close();
  }

})
