const { test, expect } = require('@playwright/test');

// 添加store
test.use({
  storageState: './tests/auth_14000001101.json'
});

test('平台代播-我的报名场次', async ({ page }) => {
  // 跳转
  await page.goto("https://live.zhipin.com/web/boss/webcast/liveContainer/mypreach/?back=header")
  // 等待网络
  await page.waitForLoadState("networkidle")
  //点击平台代码
  await page.locator('div.create-btn-card-wrap').nth(2).click();

  await page.locator('button.check-btn').click();
  // 等待页面跳转完成
  await page.waitForLoadState('networkidle');
  //获取所有代播场次，断言大于1，且核心字段不为空

  // 获取所有代播场次列表项
  const liveItems = page.locator('.live-list > div');
  // 断言代播场次数量大于1
  const liveCount = await liveItems.count();
  expect(liveCount).toBeGreaterThan(1);

  // 验证每个场次的核心字段不为空
  for (let i = 0; i < liveCount; i++) {
    const item = liveItems.nth(i);
    // 验证标题不为空
    const title = await item.locator('div > div').first().textContent();
    expect(title).toBeTruthy();
  
    // 验证日期描述不为空
    const dateDesc = await item.locator('p.desc > span').textContent();
    expect(dateDesc).toBeTruthy();
  }


});
