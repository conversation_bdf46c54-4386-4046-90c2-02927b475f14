const { test, expect } = require('@playwright/test');

// 添加store
test.use({
  storageState: './tests/auth_14000001101.json'
});

test('直播详情-首页', async ({ page }) => {
  // 跳转
  await page.goto("https://live.zhipin.com/web/boss/webcast/liveContainer/mypreach/?back=header")
  // 等待网络
  await page.waitForLoadState("networkidle")
  //点击平台代码
  await page.locator('div.create-btn-card-wrap').nth(2).click();

  await page.locator('button.check-btn').click();
  // 等待元素出现

  await page.locator(".live-list").waitFor({ state: "visible" }) 

  //获取所有代播场次，断言大于1，且核心字段不为空

  // 获取所有代播场次列表项
  const liveItems = page.locator('.live-list > div');
  // 断言代播场次数量大于1
  const liveCount = await liveItems.count();
  expect(liveCount).toBeGreaterThan(1);
  liveItems.nth(0).click();
  //点击进入直播间按钮，跳转新页面

  // 点击进入直播间按钮，跳转新页面
  const pageNewPromise = page.waitForEvent('popup');
  await page.locator('button.btn-primary').click();
  const pageNew = await pageNewPromise;
  await pageNew.waitForLoadState('networkidle');
  //新页面直播间左上角标题核心字段不为空，观看人数大于0
  // 重点：清除登录cookie
  await pageNew.context().clearCookies();
  //刷新
  await pageNew.reload()
  //等待元素出现
  await pageNew.locator(".title-container").waitFor({ state: "visible" }) 

  // 验证直播间标题不为空
  const title = await pageNew.locator('.title-container h1.title').textContent();
  expect(title).not.toBe('');

  // 验证观看人数大于0
  const viewerCountText = await pageNew.locator('.live-time').textContent();
  const viewerCount = parseInt(viewerCountText.match(/\d+/)[0]);
  expect(viewerCount).toBeGreaterThan(0);

  //验证职位城市、公告tab名字正确

  // 验证职位tab名字正确
  const jobTab = pageNew.locator('#tab-2');
  await expect(jobTab).toContainText('职位');

  // 验证城市tab名字正确
  const cityTab = pageNew.locator('#tab-3');
  await expect(cityTab).toHaveText('城市');

  // 验证公告tab名字正确
  const noticeTab = pageNew.locator('#tab-4');
  await expect(noticeTab).toHaveText('公告');

});
