const { test, expect } = require('@playwright/test');

// 添加store
test.use({
  storageState: './tests/auth_14000001101.json'
});

test('直播详情-职位列表卡片点击', async ({ page }) => {
  // 跳转
  await page.goto("https://v.zhipin.com/")
  //等待网络加载
  await page.waitForLoadState("networkidle")

  //获取直播列表所有tab，循环点击tab，验证每个tab下直播个数大于0，且核心字段不为空
  //等待
  await page.waitForTimeout(500)
  // 获取所有直播列表tab
  const tabElements = page.locator('.live-list-content .live-card');
  const tabCount = await tabElements.count();

  //断言直播列表个数大于10，随机点击一个
  // 断言直播列表个数大于10
  expect(tabCount).toBeGreaterThan(5);

  // 随机选择一个tab点击
  const randomIndex = Math.floor(Math.random() * tabCount);
  await tabElements.nth(randomIndex).click();
  //点击跳转页面后
// 等待直播详情页加载
  await page.waitForLoadState('networkidle');
  //获取所有相关推荐，断言个数大于1，循环每个推荐标题不为空，最后随机点击一个
  //等待元素出现

  await page.locator(".live-suggest-list").waitFor({ state: "visible" }) 
  // 获取所有相关推荐元素
  const recommendElements = page.locator('.live-suggest-list > .live-info');
  const recommendCount = await recommendElements.count();

  // 断言相关推荐个数大于1
  expect(recommendCount).toBeGreaterThan(1);

  // 循环验证每个推荐标题不为空
  for (let i = 0; i < recommendCount; i++) {
    const title = await recommendElements.nth(i).locator('.live-name').textContent();
    expect(title).not.toBe('');
  }
  //等待

  await page.waitForTimeout(1000)

  // 随机点击一个相关推荐
  const randomRecommendIndex = Math.floor(Math.random() * recommendCount);
  await recommendElements.nth(randomRecommendIndex).click();
  // 等待新页面加载
  await page.waitForLoadState('networkidle');

});
