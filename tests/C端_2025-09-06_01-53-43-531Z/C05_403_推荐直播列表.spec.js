//新建用例
const { test, expect } = require('@playwright/test');

test('test_name', async ({ page }) => {
  //打开
  await page.goto("https://v.zhipin.com/")
  //等待网络
  await page.waitForLoadState("networkidle")
  //获取推荐直播列表，断言大于1，且循环，断言核心字段不为空，标题、描述、城市、观看数

  // 获取推荐直播列表
  const liveItems = page.locator('.recommend-live-list > div');
  const liveCount = await liveItems.count();

  // 断言直播数量大于1
  expect(liveCount).toBeGreaterThan(1);

  // 循环验证每个直播的核心字段
  for (let i = 0; i < liveCount; i++) {
    const currentItem = liveItems.nth(i);
  
    // 验证标题不为空
    const title = await currentItem.locator('.live-name').textContent();
    expect(title).toBeTruthy();

    // 验证描述文本不为空
    const desc = await currentItem.locator('div.live-job-info span').textContent();
    expect(desc).toBeTruthy();
  
    // 验证城市信息不为空
    const city = await currentItem.locator('.live-city').textContent();
    expect(city).toBeTruthy();
  
    // 验证观看人数不为空
    const viewers = await currentItem.locator('.live-people-time span').textContent();
    expect(viewers).toBeTruthy();
    //等待
    await page.waitForTimeout(500)
  }

})
