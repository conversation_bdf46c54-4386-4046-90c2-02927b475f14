const { test, expect } = require('@playwright/test');

// 添加store
test.use({
  storageState: './tests/auth_14000001101.json'
});

test('直播详情-职位列表卡片点击', async ({ page }) => {
  // 跳转
  await page.goto("https://live.zhipin.com/web/boss/webcast/liveContainer/mypreach/?back=header")
  // 等待网络
  await page.waitForLoadState("networkidle")
  //点击进入直播控制台，打开新页面

  // 点击"进入直播控制台"链接，等待新页面打开
  const pageNewPromise = page.waitForEvent('popup');
  await page.getByText('进入直播控制台').click();
  const pageNew = await pageNewPromise;
  await pageNew.waitForLoadState('networkidle');
  //验证码输入1234点击进入直播控制台

  // 输入验证码1234
  const inputs = await pageNew.locator('.input').all();
  await inputs[0].fill('1');
  await inputs[1].fill('2');
  await inputs[2].fill('3');
  await inputs[3].fill('4');

  // 点击"进入直播控制台"按钮
  await pageNew.locator('.operate p').click();
  // 等待新页面加载完成
  await pageNew.waitForLoadState('networkidle');
  

});
