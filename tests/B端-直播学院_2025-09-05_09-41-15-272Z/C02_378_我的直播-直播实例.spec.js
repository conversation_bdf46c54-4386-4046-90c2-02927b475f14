const { test, expect } = require('@playwright/test');

// 添加store
test.use({
  storageState: './tests/auth_14000001101.json'
});

test('我的直播-直播实例', async ({ page }) => {
  // 跳转
  await page.goto("https://live.zhipin.com/web/boss/webcast/liveContainer/mypreach/?back=header")
  // 等待网络
  await page.waitForLoadState("networkidle")
  // 点击直播学院卡片
  await page.locator('div.general-live-wrap .create-btn-card-wrap').nth(0).click();
  // 等待页面加载完成
  await page.waitForLoadState('networkidle');
  // 等待
  await page.waitForTimeout(1000)

  // 基础篇中，获取所有基础篇内容，如果是视频点击后要点击class=icon-close，如果不是视频，循环点击跳转新的页面加载完毕后内容不为空，然后关闭新页面
  // 获取所有基础篇内容项
  const basicItems = await page.locator('.topic-list').nth(2).locator('.topic-item').all();

  // 使用带索引的for循环
  for (let i = 0; i < basicItems.length; i++) {
    const item = basicItems[i];
    // 检查当前项是否包含img元素
    const hasImg = await item.locator('img').count() > 0;
    if (hasImg) {
      // 点击当前基础篇内容项
      await item.click();
      await page.waitForTimeout(1000);
      // 点击有帮助按钮
      await page.getByText('有帮助').last().click();
      // 点击无帮助按钮
      await page.getByText('无帮助').last().click();
      // 如果是视频卡片，点击关闭按钮     
      await page.locator('.icon-close').last().click();
      await page.waitForTimeout(500); // 等待关闭动画
    } else {
      // 如果不是视频，打开新页面
      const newPagePromise = page.waitForEvent('popup');
      await item.click();
      const newPage = await newPagePromise;

      // 等待新页面加载完成
      await newPage.waitForLoadState('networkidle');
      // 验证页面内容不为空
      const content = await newPage.locator('.topic-detail .content').textContent();
      expect(content).not.toBe('');
      // 关闭新页面
      await newPage.close();
    }
  }
});
