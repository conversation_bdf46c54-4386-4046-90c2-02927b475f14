//新建
const { test, expect } = require('@playwright/test');

//添加store

test.use({
  storageState: './tests/auth_14000001101.json'
});

test('直播学院-列表展示', async ({ page }) => {
  //跳转
  await page.goto("https://live.zhipin.com/web/boss/webcast/liveContainer/mypreach/?back=header")
  //等待网络
  await page.waitForLoadState("networkidle")
  //点击直播学院卡片

  // 点击直播学院卡片
  await page.locator('div.general-live-wrap .create-btn-card-wrap').nth(0).click();
  // 等待页面加载完成
  await page.waitForLoadState('networkidle');
  //等待

  await page.waitForTimeout(1000)
  //获取基础篇，进阶篇，直播实例各个列表，断言核心字段不为空，数字大于0

  // 获取基础篇、进阶篇、直播实例三个板块
  const sections = await page.locator('.topic-group-title').all();
  expect(sections.length).toBeGreaterThanOrEqual(3);

  // 验证基础篇内容
  const basicItems = await page.locator('.topic-list').nth(0).locator('.topic-item').all();
  expect(basicItems.length).toBeGreaterThan(0);
  for (const item of basicItems) {
    const title = await item.locator('.topic-item-title').textContent();
    const desc = await item.locator('.topic-item-desc').textContent();
    const watchNum = await item.locator('.views-watch-num').textContent();
    const zanNum = await item.locator('.views-zan-num').textContent();
  
    expect(title).not.toBe('');
    expect(desc).not.toBe('');
    expect(parseInt(watchNum)).toBeGreaterThan(0);
    expect(parseInt(zanNum)).toBeGreaterThan(0);
  }

  // 验证进阶篇内容
  const advancedItems = await page.locator('.topic-list').nth(1).locator('.topic-item').all();
  expect(advancedItems.length).toBeGreaterThan(0);
  for (const item of advancedItems) {
    const title = await item.locator('.topic-item-title').textContent();
    const desc = await item.locator('.topic-item-desc').textContent();
    const watchNum = await item.locator('.views-watch-num').textContent();
    const zanNum = await item.locator('.views-zan-num').textContent();
  
    expect(title).not.toBe('');
    expect(desc).not.toBe('');
    expect(parseInt(watchNum)).toBeGreaterThan(0);
    expect(parseInt(zanNum)).toBeGreaterThan(0);
  }

  // 验证直播实例内容
  const exampleItems = await page.locator('.topic-list').nth(2).locator('.topic-item').all();
  expect(exampleItems.length).toBeGreaterThan(0);
  for (const item of exampleItems) {
    const title = await item.locator('.topic-item-title').textContent();
    const desc = await item.locator('.topic-item-desc').textContent();
    const watchNum = await item.locator('.views-watch-num').textContent();
    const zanNum = await item.locator('.views-zan-num').textContent();
  
    expect(title).not.toBe('');
    expect(desc).not.toBe('');
    expect(parseInt(watchNum)).toBeGreaterThan(0);
    expect(parseInt(zanNum)).toBeGreaterThan(0);
  }

  


})
