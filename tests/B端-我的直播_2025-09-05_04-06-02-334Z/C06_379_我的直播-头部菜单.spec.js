const { test, expect } = require('@playwright/test');

// 添加store
test.use({
  storageState: './tests/auth_14000001101.json'
});

test('我的直播-直播实例', async ({ page }) => {
  // 跳转
  await page.goto("https://live.zhipin.com/web/boss/webcast/liveContainer/mypreach/?back=header")
  // 等待网络
  await page.waitForLoadState("networkidle")

  //头部四个菜单获取，标题要正确，循环点击，第三个要跳转新的页面，需要跳转后在关闭
  // 获取头部四个菜单项
  const menuItems = await page.locator('.top-menu a').all();

  // 验证菜单标题正确性
  const expectedTitles = ['我的直播', '直播学院', '进入直播控制台','管理员'];
  for (let i = 0; i < menuItems.length; i++) {
    const title = await menuItems[i].textContent();
    expect(title.trim()).toBe(expectedTitles[i]);
  }

  // 循环点击菜单项
  for (let i = 0; i < menuItems.length; i++) {
    if (i === 2) {
      // 第三个菜单会打开新页面
      const newPagePromise = page.waitForEvent('popup');
      await menuItems[i].click();
      const newPage = await newPagePromise;
      await newPage.waitForLoadState('networkidle');
      await newPage.close();
    } else {
      await menuItems[i].click();
      // 等待页面稳定
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(1000)
      //返回
      await page.goBack()
      await page.waitForTimeout(1000)
    }
  }

});
