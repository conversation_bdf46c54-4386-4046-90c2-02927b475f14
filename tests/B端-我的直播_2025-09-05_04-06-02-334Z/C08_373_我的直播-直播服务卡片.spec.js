//新建
const { test, expect } = require('@playwright/test');

//添加store
test.use({
  storageState: './tests/auth_14000001101.json'
});

test('我的直播-直播服务卡片', async ({ page }) => {
  //跳转
  await page.goto("https://live.zhipin.com/web/boss/webcast/liveContainer/mypreach/?back=header")
  //等待网络
  await page.waitForLoadState("networkidle")

  //直播服务下四个卡片，断言标题和描述不为空，然后循环点击，等待页面加载完毕后返回，继续

  // 获取直播服务下的四个卡片
  const serviceCards = page.locator('.general-live-wrap > div');

  // 断言四个卡片存在
  const cardCount = await serviceCards.count();
  expect(cardCount).toBe(3);

  // 循环处理每个卡片
  for (let i = 0; i < cardCount; i++) {
    const currentCard = serviceCards.nth(i);

    // 获取标题和描述元素
    const title = currentCard.locator('div > div > div:first-child');
    const desc = currentCard.locator('div > div > div:nth-child(2)');

    // 断言标题和描述不为空
    await expect(title).not.toBeEmpty();
    await expect(desc).not.toBeEmpty();

    await currentCard.click();
    // 等待页面加载
    await page.waitForLoadState('networkidle');
    //等待

    await page.waitForTimeout(1000)
    // 返回上一页
    await page.goBack();
  }


})
