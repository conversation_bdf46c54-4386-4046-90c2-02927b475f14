const { test, expect } = require('@playwright/test');

// 添加store
test.use({
  storageState: './tests/auth_14000001101.json'
});

test('我的直播-平台代播', async ({ page }) => {
  // 跳转
  await page.goto("https://live.zhipin.com/web/boss/webcast/liveContainer/mypreach/?back=header")
  // 等待网络
  await page.waitForLoadState("networkidle")
  //点击平台代码
  // 点击平台代播服务
  await page.locator('div.create-btn-card-wrap').nth(2).click();
  //断言核心字段不为空

  // 断言核心字段不为空
  const contentElements = page.locator('.desc .content');
  const count = await contentElements.count();
  expect(count).toBeGreaterThan(0);

  for (let i = 0; i < count; i++) {
    const content = await contentElements.nth(i).textContent();
    expect(content).not.toBe('');
  }

  const resumeElements = page.locator('.desc .resume');
  const resumeCount = await resumeElements.count();
  expect(resumeCount).toBeGreaterThan(0);

  for (let j = 0; j < resumeCount; j++) {
    const resumeText = await resumeElements.nth(j).textContent();
    expect(resumeText).not.toBe('');
  }

  // 验证查看按钮可见
  await expect(page.locator('.check-btn')).toBeVisible();


});
