const { test, expect } = require('@playwright/test');

// 添加store
test.use({
  storageState: './tests/auth_14000001101.json'
});

test('直播详情-职位列表卡片点击', async ({ page }) => {
  // 跳转
  await page.goto("https://live.zhipin.com/web/boss/webcast/liveContainer/mypreach/?back=header")
  // 等待网络
  await page.waitForLoadState("networkidle")
  //查看全部
  await page.locator('.live-list-title-btn').click();
  await page.waitForLoadState("networkidle")
  //等待
  await page.waitForTimeout(1000)
  //断言直播次数大于0
  // 获取直播次数元素并断言大于0
  const liveCount = await page.locator('.live-data-content div:first-child div:first-child').textContent();
  expect(Number(liveCount)).toBeGreaterThan(0);

  //获取所有已结束的直播记录，断言个数大于5，循环判断核心字段不为空
  // 获取所有已结束的直播记录卡片
  const endedLiveCards = page.locator('.reserved-live-list .record-card-wrapper');
  const endedLiveCount = await endedLiveCards.count();

  // 断言已结束的直播记录个数大于5
  expect(endedLiveCount).toBeGreaterThan(5);

  // 循环检查每个直播记录的核心字段不为空
  for (let i = 0; i < endedLiveCount; i++) {
    const currentCard = endedLiveCards.nth(i);
 
    // 验证标题不为空
    const title = await currentCard.locator('h4.title').textContent();
    expect(title).toBeTruthy();

    // 验证主播信息不为空
    const hostInfo = await currentCard.locator('div.person-info-container').nth(0).locator('span.user').textContent();
    expect(hostInfo).toBeTruthy();

    // 验证主播账号不为空
    const hostAccount = await currentCard.locator('div.person-info-container').nth(1).locator('span.user').textContent();
    expect(hostAccount).toBeTruthy();

    // 验证时间不为空
    const time = await currentCard.locator('div.time-info').nth(1).locator('span').nth(0).textContent();
    expect(time).toBeTruthy();
  }

});
