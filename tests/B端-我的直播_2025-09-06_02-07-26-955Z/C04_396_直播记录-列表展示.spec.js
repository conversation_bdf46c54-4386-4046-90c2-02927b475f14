const { test, expect } = require('@playwright/test');

// 添加store
test.use({
  storageState: './tests/auth_14000001101.json'
});

test('直播详情-职位列表卡片点击', async ({ page }) => {
  // 跳转
  await page.goto("https://live.zhipin.com/web/boss/webcast/liveContainer/mypreach/?back=header")
  // 等待网络
  await page.waitForLoadState("networkidle")
  //获取直播记录，断言长度大于0，且核心字段不为空

  // 获取直播记录列表
  const liveRecords = page.locator('.live-list > div');
  const count = await liveRecords.count();
  // 断言直播记录数量大于0
  expect(count).toBeGreaterThan(0);
  //循环验证直播记录中每个核心字段不为空，标题、信息、账号、时间等

  // 循环验证每个直播记录的核心字段
    for (let i = 0; i < count; i++) {
      const currentRecord = liveRecords.nth(i);
    
      // 验证标题不为空
      const title = await currentRecord.locator('h4.title').textContent();
      expect(title).toBeTruthy();
    
      // 验证主播信息不为空
      const hostInfo = await currentRecord.locator('div.person-info-container').nth(0).locator('span.user').textContent();
      expect(hostInfo).toBeTruthy();
    
      // 验证主播账号不为空
      const hostAccount = await currentRecord.locator('div.person-info-container').nth(1).locator('span.user').textContent();
      expect(hostAccount).toBeTruthy();
    
      // 验证时间不为空
      const time = await currentRecord.locator('div.time-info').nth(1).locator('span').nth(0).textContent();
      expect(time).toBeTruthy();
    }

});
