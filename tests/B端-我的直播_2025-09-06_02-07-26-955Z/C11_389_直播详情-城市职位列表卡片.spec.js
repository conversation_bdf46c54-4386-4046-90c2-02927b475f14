const { test, expect } = require('@playwright/test');

// 添加store
test.use({
  storageState: './tests/auth_14000001101.json'
});

test('直播详情-职位列表卡片点击', async ({ page }) => {
  // 跳转
  await page.goto("https://live.zhipin.com/web/boss/webcast/liveContainer/mypreach/?back=header")
  // 等待网络
  await page.waitForLoadState("networkidle")
  //点击平台代码
  await page.locator('div.create-btn-card-wrap').nth(2).click();

  await page.locator('button.check-btn').click();
  // 等待元素出现

  await page.locator(".live-list").waitFor({ state: "visible" })

  //获取所有代播场次，断言大于1，且核心字段不为空

  // 获取所有代播场次列表项
  const liveItems = page.locator('.live-list > div');
  // 断言代播场次数量大于1
  const liveCount = await liveItems.count();
  expect(liveCount).toBeGreaterThan(1);
  liveItems.nth(0).click();

  // 点击进入直播间按钮，跳转新页面
  const pageNewPromise = page.waitForEvent('popup');
  await page.locator('button.btn-primary').click();
  const pageNew = await pageNewPromise;
  await pageNew.waitForLoadState('networkidle');

  await pageNew.context().clearCookies();
  await pageNew.reload()
  await pageNew.locator("#tab-2").waitFor({ state: "visible" })

  //点击城市tab，获取下边所有子城市tab，循环点击

  // 点击城市tab
  await pageNew.locator('#tab-3').click();
  // 等待城市列表加载
  await pageNew.locator('.job-item').first().waitFor({ state: 'visible' });

  // 获取所有子城市tab
  const cityTabs = pageNew.locator('.job-box .job-item');
  const cityCount = await cityTabs.count();
  //断言cityCount大于0
  expect(cityCount).toBeGreaterThan(0);

  // 循环点击每个城市tab
  for (let i = 0; i < cityCount; i++) {
    await cityTabs.nth(i).click();
    //获取城市下边的所有职位卡片，循环断言核心字段不为空
    //等待

    await page.waitForTimeout(200)
    // 获取当前城市下的所有职位卡片
    const jobCards = pageNew.locator('div.job-list > div');
    const jobCount = await jobCards.count();
    expect(jobCount).toBeGreaterThan(0);

    //随机点击一个jobCards，跳转新页面，加载完毕后关闭新页面

    // 随机选择一个职位卡片
    const randomIndex = Math.floor(Math.random() * jobCount);
    const jobCard = jobCards.nth(randomIndex);

    // 点击职位卡片，跳转新页面
    const jobPagePromise = pageNew.waitForEvent('popup');
    await jobCard.click();
    const jobPage = await jobPagePromise;
    await jobPage.waitForLoadState('networkidle');

    //等待

    await page.waitForTimeout(300)
    // 关闭职位详情页面
    await jobPage.close();
  }


});
