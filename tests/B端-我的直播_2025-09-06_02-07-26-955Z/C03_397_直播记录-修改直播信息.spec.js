const { test, expect } = require('@playwright/test');

// 添加store
test.use({
  storageState: './tests/auth_14000001101.json'
});

test('直播详情-职位列表卡片点击', async ({ page }) => {
  // 跳转
  await page.goto("https://live.zhipin.com/web/boss/webcast/liveContainer/mypreach/?back=header")
  // 等待网络
  await page.waitForLoadState("networkidle")
  //获取直播记录，断言长度大于0，且核心字段不为空

  // 获取直播记录列表
  const liveRecords = page.locator('.live-list > div');
  const count = await liveRecords.count();
  // 断言直播记录数量大于0
  expect(count).toBeGreaterThan(0);
  //随机点击一个修改直播信息

  // 随机点击一个修改直播信息按钮
  const modifyButtons = page.locator('.btn.btn-primary.btn-outline.btn-mid:has-text("修改直播信息")');
  const buttonCount = await modifyButtons.count();
  const randomIndex = Math.floor(Math.random() * buttonCount);
  await modifyButtons.nth(randomIndex).click();
  // 等待修改页面加载
  await page.waitForLoadState('networkidle');
  //等待

  await page.waitForTimeout(300)
  //点击下一步
  await page.locator('.btn.btn-primary.nextStep').click();
  //点击完成
  await page.locator('.btn.btn-primary.nextStep').click();


});
