const { test, expect } = require('@playwright/test');

// 添加store
test.use({
  storageState: './tests/auth_14000001101.json'
});

test('预约直播-品牌', async ({ page }) => {
  // 跳转
  await page.goto("https://live.zhipin.com/web/boss/webcast/liveContainer/mypreach/?back=header")
  // 等待网络
  await page.waitForLoadState("networkidle")
  //点击平台代码
  await page.locator('div.create-btn-card-wrap').nth(3).click();
  //点击品牌，断言品牌列表长度大于1，搜索品牌，搜索结果列表大于0，随机点击一个，点击保存

  // 点击品牌输入框
  await page.locator('input[placeholder="请选择品牌"]').click();
  // 等待
  await page.waitForTimeout(200)
  // 断言品牌列表长度大于1
  const brandItems = page.locator('.detail-container > div > div > div');
  const brandCount = await brandItems.count();
  expect(brandCount).toBeGreaterThan(1);

  // 搜索品牌
  await page.locator('input[placeholder="搜索品牌名"]').fill('春招');
  // 等待搜索结果
  await page.waitForTimeout(500); // 等待搜索请求完成
  // 断言搜索结果列表大于0
  const searchResults = page.locator('.detail-container > div > div > div');
  const resultCount = await searchResults.count();
  expect(resultCount).toBeGreaterThan(0);

  // 随机点击一个搜索结果
  const randomIndex = Math.floor(Math.random() * resultCount);
  await searchResults.nth(randomIndex).click();
  // 点击保存按钮
  await page.locator('.save-btn').click();



});
