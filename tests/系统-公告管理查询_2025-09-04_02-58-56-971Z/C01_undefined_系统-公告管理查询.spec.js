//新建用例
const { test, expect } = require('@playwright/test');

test('test_name', async ({ page }) => {

  //跳转
  await page.goto("https://kefu.zhipin.com/home/<USER>/user")
  //通过文本点击
  await page.locator("text=公告管理").click()
  //针对公告管理查询条件编写自动化测试用例，每个搜索字段都要操作

  // 填写公告标题搜索条件
  await page.locator('.ui-form-prefix-label input[placeholder="请输入"]').first().fill('');

  // 选择类型搜索条件
  await page.locator('.ui-select-multiple div:has-text("请选择")').nth(0).click();
  await page.locator('text=规章制度').nth(0).click();

  // 选择置顶搜索条件
  await page.locator('.ui-form-prefix-label div:has-text("请选择")').nth(1).click();
  await page.locator('text=否').nth(1).click();

  // 填写更新时间范围
  await page.locator('.ui-range-input').first().fill('2025-01-01');
  await page.locator('.ui-range-input').nth(1).fill('2025-12-31');

  // 选择状态搜索条件
  await page.locator('.ui-form-prefix-label div:has-text("请选择")').last().click();
  //已发布
  await page.locator('text=已发布').nth(0).click();

  // 点击查询按钮
  await page.locator('button:has-text("查询")').click();

  // 等待
  await page.waitForTimeout(1000)
  // 验证查询结果不为空
  const resultCount = await page.locator('.table-body tr').count();
  expect(resultCount).toBeGreaterThan(5);

})
