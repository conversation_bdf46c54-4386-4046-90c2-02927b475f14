//新建
const { test, expect } = require('@playwright/test');

//添加store

test.use({
  storageState: './tests/auth_14000001101.json'
});

test('我的直播-公司直播券展示', async ({ page }) => {
  //跳转
  await page.goto("https://live.zhipin.com/web/boss/webcast/liveContainer/mypreach/?back=header")
  //等待网络
  await page.waitForLoadState("networkidle")
  //查看公司直播券，断言核心字段不为空，点击使用

  // 定位公司直播券容器并断言核心字段不为空
  const companyTicket = page.locator('.company-live-ticket-container');
  await expect(companyTicket.locator('.ticket-text-title')).not.toBeEmpty();
  await expect(companyTicket.locator('.ticket-text-desc')).not.toBeEmpty();

  // 点击使用按钮
  await companyTicket.locator('.ticket-use-btn').click();
  // 等待可能出现的弹窗加载
  await page.waitForLoadState('networkidle');
  //等待
  await page.waitForTimeout(1500)
  //返回
  await page.goBack()

  


})
