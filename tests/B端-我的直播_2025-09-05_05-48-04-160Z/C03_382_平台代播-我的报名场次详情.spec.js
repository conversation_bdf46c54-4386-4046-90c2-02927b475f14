const { test, expect } = require('@playwright/test');

// 添加store
test.use({
  storageState: './tests/auth_14000001101.json'
});

test('平台代播-我的报名场次', async ({ page }) => {
  // 跳转
  await page.goto("https://live.zhipin.com/web/boss/webcast/liveContainer/mypreach/?back=header")
  // 等待网络
  await page.waitForLoadState("networkidle")
  //点击平台代码
  await page.locator('div.create-btn-card-wrap').nth(2).click();

  await page.locator('button.check-btn').click();
  // 等待元素出现

  await page.locator(".live-list").waitFor({ state: "visible" }) 

  //获取所有代播场次，断言大于1，且核心字段不为空

  // 获取所有代播场次列表项
  const liveItems = page.locator('.live-list > div');
  // 断言代播场次数量大于1
  const liveCount = await liveItems.count();
  expect(liveCount).toBeGreaterThan(1);

  // 验证每个场次的核心字段不为空
  for (let i = 0; i < liveCount; i++) {
    const item = liveItems.nth(i);
    await item.click();
    //验证核心字段不为空
    await page.waitForTimeout(500)

    //如果元素 my-proxy-container proxy_signUp_success 存在，继续后边操作

    // 检查是否存在报名成功页面
    const successPage = page.locator('.my-proxy-container.proxy_signUp_success');
    if (await successPage.isVisible()) {
      // 验证核心字段不为空
      const title = await page.locator('.title-box .title').textContent();
      expect(title).not.toBe('');

      const jobType = await page.locator('.session-wrap .job-type-tag-wrap span').textContent();
      expect(jobType).not.toBe('');

      const experience = await page.locator('.session-wrap .value').nth(0).textContent();
      expect(experience).not.toBe('');

      const liveTime = await page.locator('.session-wrap .value').nth(1).textContent();
      expect(liveTime).not.toBe('');

      const city = await page.locator('.session-wrap .value').nth(2).textContent();
      expect(city).not.toBe('');
      // 点击返回首页按钮
      await page.locator('.back-btn').click();
      await page.locator('button.check-btn').click();
      //等待
      await page.waitForTimeout(500)
    } else {
      await page.locator('button.check-btn').click();
      //等待
      await page.waitForTimeout(500)
      
    }



  }


});
