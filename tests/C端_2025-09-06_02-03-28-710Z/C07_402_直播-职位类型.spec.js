//新建用例
const { test, expect } = require('@playwright/test');

test('test_name', async ({ page }) => {
  //打开
  await page.goto("https://v.zhipin.com/")
  //等待网络
  await page.waitForLoadState("networkidle")


  //获取所有职位类型，断言个数大于50，然后随机点击一个

  // 获取所有职位类型元素
  const jobTypeElements = page.locator('.job-search-common .search-condition .content-div');
  // 断言职位类型数量大于50
  const jobTypeCount = await jobTypeElements.count();
  expect(jobTypeCount).toBeGreaterThan(10);
  //随机点击一个职位类型
  // 随机选择一个职位类型点击
  const randomIndex = Math.floor(Math.random() * jobTypeCount);
  await jobTypeElements.nth(randomIndex).click();
  // 等待页面加载完成
  await page.waitForLoadState('networkidle');

  //获取直播列表，断言长度大于3

  // 获取直播列表元素
  const liveListElements = page.locator('.live-list-content > div');
  // 断言直播列表数量大于3
  const liveListCount = await liveListElements.count();
  expect(liveListCount).toBeGreaterThan(3);


})
