const { test, expect } = require('@playwright/test');


test('直播详情-职位列表卡片点击', async ({ page }) => {
  // 跳转
  await page.goto("https://v.zhipin.com/")
  //等待网络加载
  await page.waitForLoadState("networkidle")

  //获取直播列表所有tab，循环点击tab，验证每个tab下直播个数大于0，且核心字段不为空

  // 获取所有直播列表tab
  const tabElements = page.locator('.job-type-list .type-name');
  const tabCount = await tabElements.count();

  // 循环点击每个tab并验证内容
  for (let i = 0; i < tabCount; i++) {
    const tab = tabElements.nth(i);

    // 点击当前tab
    await tab.click();
    await page.waitForLoadState('networkidle');
    //等待
    await page.waitForTimeout(300)

    // 获取当前tab下的直播列表
    const liveList = page.locator('.live-list-content .live-card');
    const liveCount = await liveList.count();

    // 验证直播个数大于0
    expect(liveCount).toBeGreaterThan(0);

    // 验证每个直播的核心字段不为空
    for (let j = 0; j < liveCount; j++) {
      const currentItem = liveList.nth(j);

      // 验证标题不为空
      const title = await currentItem.locator('.live-name').textContent();
      expect(title).toBeTruthy();

      // 验证描述文本不为空
      const desc = await currentItem.locator('div.flex-center span').textContent();
      expect(desc).toBeTruthy();

      //  // 验证城市信息不为空
      const city = await currentItem.locator('.live-city').textContent();
      expect(city).toBeTruthy();

      //  // 验证观看人数不为空
      const viewers = await currentItem.locator('.live-people-time span').textContent();
      expect(viewers).toBeTruthy();
      //等待
    }
    await page.waitForTimeout(500)
    await tabElements.nth(0).click();

  }

});
