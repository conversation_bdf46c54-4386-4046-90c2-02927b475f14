const { test, expect } = require('@playwright/test');


test('直播详情-职位列表卡片点击', async ({ page }) => {
  // 跳转
  await page.goto("https://v.zhipin.com/")
  //等待网络加载
  await page.waitForLoadState("networkidle")

  //获取直播列表所有tab，循环点击tab，验证每个tab下直播个数大于0，且核心字段不为空
  //等待
  await page.waitForTimeout(500)
  // 获取所有直播列表tab
  const tabElements = page.locator('.live-list-content .live-card');
  const tabCount = await tabElements.count();

  //断言直播列表个数大于10，随机点击一个
  // 断言直播列表个数大于10
  expect(tabCount).toBeGreaterThan(10);

  // 随机选择一个tab点击
  const randomIndex = Math.floor(Math.random() * tabCount);
  await tabElements.nth(randomIndex).click();
  //点击跳转页面后
// 等待直播详情页加载
  await page.waitForLoadState('networkidle');
  //如果有职位tab的话，点击职位tab，获取职位下所有职位列表，循环断言列表核心字段不为空
  //等待元素存在

  await page.locator(".right-live-func").waitFor({ state: "visible" }) 
  // 检查是否存在职位tab
  await page.locator('#tab-4').click();
  
});
