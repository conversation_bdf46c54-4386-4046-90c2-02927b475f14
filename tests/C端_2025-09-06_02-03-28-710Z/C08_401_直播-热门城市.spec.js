//新建用例
const { test, expect } = require('@playwright/test');

test('直播-热门城市', async ({ page }) => {
  //打开
  await page.goto("https://v.zhipin.com/")
  //等待网络
  await page.waitForLoadState("networkidle")


  //点击展开，获取所有热门城市，断言个数大于50，然后随机点击5个

  // 点击展开热门城市
  await page.locator('.city-filter span').click();

  // 获取所有热门城市元素
  const cityElements = page.locator('.hot-city a.content');
  const cityCount = await cityElements.count();

  // 断言热门城市数量大于50
  expect(cityCount).toBeGreaterThan(50);

  // 随机点击5个城市
  const randomIndexes = [];
  while (randomIndexes.length < 5) {
    const randomIndex = Math.floor(Math.random() * cityCount);
    if (!randomIndexes.includes(randomIndex)) {
      randomIndexes.push(randomIndex);
      await cityElements.nth(randomIndex).click();
      await page.waitForLoadState('networkidle');
    }
    //获取直播列表，断言长度大于0
    //等待

    await page.waitForTimeout(500)
    // 获取直播列表元素
    const liveListElements = page.locator('.live-list-content > div');
    const liveCount = await liveListElements.count();

    // 断言直播列表数量大于0
    expect(liveCount).toBeGreaterThan(0);
  }

})
